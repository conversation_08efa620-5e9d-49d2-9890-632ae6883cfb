{"version": 3, "sources": ["PanGestureHandler.ts"], "names": ["State", "DEFAULT_TOUCH_SLOP", "WheelDevice", "Gesture<PERSON>andler", "DEFAULT_MIN_POINTERS", "DEFAULT_MAX_POINTERS", "DEFAULT_MIN_DIST_SQ", "PanGestureHandler", "Number", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "UNDETERMINED", "updateGestureConfig", "enabled", "props", "resetConfig", "checkCustomActivationCriteria", "customActivationProperties", "config", "minDist", "undefined", "minDistSq", "hasCustomActivationCriteria", "minPointers", "maxPointers", "minVelocity", "minVelocityX", "minVelocityY", "activateAfterLongPress", "activeOffsetXStart", "activeOffsetXEnd", "failOffsetXStart", "failOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "failOffsetYStart", "failOffsetYEnd", "enableTrackpadTwoFingerGesture", "minVelocitySq", "transformNativeEvent", "translationX", "getTranslationX", "translationY", "getTranslationY", "isNaN", "velocityX", "velocityY", "stylusData", "lastX", "startX", "offsetX", "lastY", "startY", "offsetY", "clearActivationTimeout", "clearTimeout", "activationTimeout", "onPointerDown", "event", "isButtonInConfig", "button", "tracker", "addToTracker", "lastCoords", "getAbsoluteCoordsAverage", "x", "y", "tryBegin", "<PERSON><PERSON><PERSON>", "tryToSendTouchEvent", "onPointerAdd", "trackedPointersCount", "state", "ACTIVE", "cancel", "fail", "onPointerUp", "removeFromTracker", "pointerId", "end", "resetProgress", "onPointerRemove", "onPointerMove", "track", "velocity", "getVelocity", "onPointerOutOfBounds", "shouldCancelWhenOutside", "scheduleWheelEnd", "endWheelTimeout", "setTimeout", "wheelDevice", "onWheel", "MOUSE", "wheelDeltaY", "TOUCHPAD", "begin", "activate", "tryToSendMoveEvent", "shouldActivate", "dx", "dy", "distanceSq", "vx", "vy", "velocitySq", "shouldFail", "BEGAN", "force", "onCancel", "onReset"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAASC,kBAAT,QAAmC,cAAnC;AACA,SAA2CC,WAA3C,QAA8D,eAA9D;AAEA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,MAAMC,oBAAoB,GAAG,CAA7B;AACA,MAAMC,oBAAoB,GAAG,EAA7B;AACA,MAAMC,mBAAmB,GAAGL,kBAAkB,GAAGA,kBAAjD;AAEA,eAAe,MAAMM,iBAAN,SAAgCJ,cAAhC,CAA+C;AAAA;AAAA;;AAAA,wDACJ,CACtD,oBADsD,EAEtD,kBAFsD,EAGtD,kBAHsD,EAItD,gBAJsD,EAKtD,oBALsD,EAMtD,kBANsD,EAOtD,kBAPsD,EAQtD,gBARsD,EAStD,cATsD,EAUtD,cAVsD,EAWtD,aAXsD,CADI;;AAAA,uCAezC,CAfyC;;AAAA,uCAgBzC,CAhByC;;AAAA,uCAkBxCG,mBAlBwC;;AAAA,gDAoB/B,CAACE,MAAM,CAACC,gBApBuB;;AAAA,8CAqBjCD,MAAM,CAACE,gBArB0B;;AAAA,8CAsBjCF,MAAM,CAACE,gBAtB0B;;AAAA,4CAuBnCF,MAAM,CAACC,gBAvB4B;;AAAA,gDAyB/BD,MAAM,CAACC,gBAzBwB;;AAAA,8CA0BjCD,MAAM,CAACE,gBA1B0B;;AAAA,8CA2BjCF,MAAM,CAACE,gBA3B0B;;AAAA,4CA4BnCF,MAAM,CAACC,gBA5B4B;;AAAA,0CA8BrCD,MAAM,CAACC,gBA9B8B;;AAAA,0CA+BrCD,MAAM,CAACC,gBA/B8B;;AAAA,2CAgCpCD,MAAM,CAACC,gBAhC6B;;AAAA,yCAkCtCL,oBAlCsC;;AAAA,yCAmCtCC,oBAnCsC;;AAAA,oCAqC3C,CArC2C;;AAAA,oCAsC3C,CAtC2C;;AAAA,qCAuC1C,CAvC0C;;AAAA,qCAwC1C,CAxC0C;;AAAA,mCAyC5C,CAzC4C;;AAAA,mCA0C5C,CA1C4C;;AAAA;;AAAA,oDA8C3B,CA9C2B;;AAAA,+CA+ChC,CA/CgC;;AAAA,4DAiDnB,KAjDmB;;AAAA,6CAkDlC,CAlDkC;;AAAA,yCAmDtCH,WAAW,CAACS,YAnD0B;AAAA;;AAqDrDC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,SAAKC,WAAL;AAEA,UAAMH,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;AACA,SAAKE,6BAAL,CAAmC,KAAKC,0BAAxC;;AAEA,QAAI,KAAKC,MAAL,CAAYC,OAAZ,KAAwBC,SAA5B,EAAuC;AACrC,WAAKC,SAAL,GAAiB,KAAKH,MAAL,CAAYC,OAAZ,GAAsB,KAAKD,MAAL,CAAYC,OAAnD;AACD,KAFD,MAEO,IAAI,KAAKG,2BAAT,EAAsC;AAC3C,WAAKD,SAAL,GAAiBb,MAAM,CAACC,gBAAxB;AACD;;AAED,QAAI,KAAKS,MAAL,CAAYK,WAAZ,KAA4BH,SAAhC,EAA2C;AACzC,WAAKG,WAAL,GAAmB,KAAKL,MAAL,CAAYK,WAA/B;AACD;;AAED,QAAI,KAAKL,MAAL,CAAYM,WAAZ,KAA4BJ,SAAhC,EAA2C;AACzC,WAAKI,WAAL,GAAmB,KAAKN,MAAL,CAAYM,WAA/B;AACD;;AAED,QAAI,KAAKN,MAAL,CAAYO,WAAZ,KAA4BL,SAAhC,EAA2C;AACzC,WAAKM,YAAL,GAAoB,KAAKR,MAAL,CAAYO,WAAhC;AACA,WAAKE,YAAL,GAAoB,KAAKT,MAAL,CAAYO,WAAhC;AACD;;AAED,QAAI,KAAKP,MAAL,CAAYQ,YAAZ,KAA6BN,SAAjC,EAA4C;AAC1C,WAAKM,YAAL,GAAoB,KAAKR,MAAL,CAAYQ,YAAhC;AACD;;AAED,QAAI,KAAKR,MAAL,CAAYS,YAAZ,KAA6BP,SAAjC,EAA4C;AAC1C,WAAKO,YAAL,GAAoB,KAAKT,MAAL,CAAYS,YAAhC;AACD;;AAED,QAAI,KAAKT,MAAL,CAAYU,sBAAZ,KAAuCR,SAA3C,EAAsD;AACpD,WAAKQ,sBAAL,GAA8B,KAAKV,MAAL,CAAYU,sBAA1C;AACD;;AAED,QAAI,KAAKV,MAAL,CAAYW,kBAAZ,KAAmCT,SAAvC,EAAkD;AAChD,WAAKS,kBAAL,GAA0B,KAAKX,MAAL,CAAYW,kBAAtC;;AAEA,UAAI,KAAKX,MAAL,CAAYY,gBAAZ,KAAiCV,SAArC,EAAgD;AAC9C,aAAKU,gBAAL,GAAwBtB,MAAM,CAACC,gBAA/B;AACD;AACF;;AAED,QAAI,KAAKS,MAAL,CAAYY,gBAAZ,KAAiCV,SAArC,EAAgD;AAC9C,WAAKU,gBAAL,GAAwB,KAAKZ,MAAL,CAAYY,gBAApC;;AAEA,UAAI,KAAKZ,MAAL,CAAYW,kBAAZ,KAAmCT,SAAvC,EAAkD;AAChD,aAAKS,kBAAL,GAA0BrB,MAAM,CAACE,gBAAjC;AACD;AACF;;AAED,QAAI,KAAKQ,MAAL,CAAYa,gBAAZ,KAAiCX,SAArC,EAAgD;AAC9C,WAAKW,gBAAL,GAAwB,KAAKb,MAAL,CAAYa,gBAApC;;AAEA,UAAI,KAAKb,MAAL,CAAYc,cAAZ,KAA+BZ,SAAnC,EAA8C;AAC5C,aAAKY,cAAL,GAAsBxB,MAAM,CAACC,gBAA7B;AACD;AACF;;AAED,QAAI,KAAKS,MAAL,CAAYc,cAAZ,KAA+BZ,SAAnC,EAA8C;AAC5C,WAAKY,cAAL,GAAsB,KAAKd,MAAL,CAAYc,cAAlC;;AAEA,UAAI,KAAKd,MAAL,CAAYa,gBAAZ,KAAiCX,SAArC,EAAgD;AAC9C,aAAKW,gBAAL,GAAwBvB,MAAM,CAACE,gBAA/B;AACD;AACF;;AAED,QAAI,KAAKQ,MAAL,CAAYe,kBAAZ,KAAmCb,SAAvC,EAAkD;AAChD,WAAKa,kBAAL,GAA0B,KAAKf,MAAL,CAAYe,kBAAtC;;AAEA,UAAI,KAAKf,MAAL,CAAYgB,gBAAZ,KAAiCd,SAArC,EAAgD;AAC9C,aAAKc,gBAAL,GAAwB1B,MAAM,CAACC,gBAA/B;AACD;AACF;;AAED,QAAI,KAAKS,MAAL,CAAYgB,gBAAZ,KAAiCd,SAArC,EAAgD;AAC9C,WAAKc,gBAAL,GAAwB,KAAKhB,MAAL,CAAYgB,gBAApC;;AAEA,UAAI,KAAKhB,MAAL,CAAYe,kBAAZ,KAAmCb,SAAvC,EAAkD;AAChD,aAAKa,kBAAL,GAA0BzB,MAAM,CAACE,gBAAjC;AACD;AACF;;AAED,QAAI,KAAKQ,MAAL,CAAYiB,gBAAZ,KAAiCf,SAArC,EAAgD;AAC9C,WAAKe,gBAAL,GAAwB,KAAKjB,MAAL,CAAYiB,gBAApC;;AAEA,UAAI,KAAKjB,MAAL,CAAYkB,cAAZ,KAA+BhB,SAAnC,EAA8C;AAC5C,aAAKgB,cAAL,GAAsB5B,MAAM,CAACC,gBAA7B;AACD;AACF;;AAED,QAAI,KAAKS,MAAL,CAAYkB,cAAZ,KAA+BhB,SAAnC,EAA8C;AAC5C,WAAKgB,cAAL,GAAsB,KAAKlB,MAAL,CAAYkB,cAAlC;;AAEA,UAAI,KAAKlB,MAAL,CAAYiB,gBAAZ,KAAiCf,SAArC,EAAgD;AAC9C,aAAKe,gBAAL,GAAwB3B,MAAM,CAACE,gBAA/B;AACD;AACF;;AAED,QAAI,KAAKQ,MAAL,CAAYmB,8BAAZ,KAA+CjB,SAAnD,EAA8D;AAC5D,WAAKiB,8BAAL,GACE,KAAKnB,MAAL,CAAYmB,8BADd;AAED;AACF;;AAEStB,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AAEA,SAAKc,kBAAL,GAA0B,CAACrB,MAAM,CAACC,gBAAlC;AACA,SAAKqB,gBAAL,GAAwBtB,MAAM,CAACE,gBAA/B;AACA,SAAKqB,gBAAL,GAAwBvB,MAAM,CAACE,gBAA/B;AACA,SAAKsB,cAAL,GAAsBxB,MAAM,CAACC,gBAA7B;AAEA,SAAKwB,kBAAL,GAA0BzB,MAAM,CAACC,gBAAjC;AACA,SAAKyB,gBAAL,GAAwB1B,MAAM,CAACE,gBAA/B;AACA,SAAKyB,gBAAL,GAAwB3B,MAAM,CAACE,gBAA/B;AACA,SAAK0B,cAAL,GAAsB5B,MAAM,CAACC,gBAA7B;AAEA,SAAKiB,YAAL,GAAoBlB,MAAM,CAACC,gBAA3B;AACA,SAAKkB,YAAL,GAAoBnB,MAAM,CAACC,gBAA3B;AACA,SAAK6B,aAAL,GAAqB9B,MAAM,CAACC,gBAA5B;AAEA,SAAKY,SAAL,GAAiBf,mBAAjB;AAEA,SAAKiB,WAAL,GAAmBnB,oBAAnB;AACA,SAAKoB,WAAL,GAAmBnB,oBAAnB;AAEA,SAAKuB,sBAAL,GAA8B,CAA9B;AACD;;AAESW,EAAAA,oBAAoB,GAAG;AAC/B,UAAMC,YAAoB,GAAG,KAAKC,eAAL,EAA7B;AACA,UAAMC,YAAoB,GAAG,KAAKC,eAAL,EAA7B;AAEA,WAAO,EACL,GAAG,MAAMJ,oBAAN,EADE;AAELC,MAAAA,YAAY,EAAEI,KAAK,CAACJ,YAAD,CAAL,GAAsB,CAAtB,GAA0BA,YAFnC;AAGLE,MAAAA,YAAY,EAAEE,KAAK,CAACF,YAAD,CAAL,GAAsB,CAAtB,GAA0BA,YAHnC;AAILG,MAAAA,SAAS,EAAE,KAAKA,SAJX;AAKLC,MAAAA,SAAS,EAAE,KAAKA,SALX;AAMLC,MAAAA,UAAU,EAAE,KAAKA;AANZ,KAAP;AAQD;;AAEON,EAAAA,eAAe,GAAW;AAChC,WAAO,KAAKO,KAAL,GAAa,KAAKC,MAAlB,GAA2B,KAAKC,OAAvC;AACD;;AACOP,EAAAA,eAAe,GAAW;AAChC,WAAO,KAAKQ,KAAL,GAAa,KAAKC,MAAlB,GAA2B,KAAKC,OAAvC;AACD;;AAEOC,EAAAA,sBAAsB,GAAS;AACrCC,IAAAA,YAAY,CAAC,KAAKC,iBAAN,CAAZ;AACD,GAhN2D,CAkN5D;;;AACUC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,QAAI,CAAC,KAAKC,gBAAL,CAAsBD,KAAK,CAACE,MAA5B,CAAL,EAA0C;AACxC;AACD;;AAED,SAAKC,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,SAAKX,UAAL,GAAkBW,KAAK,CAACX,UAAxB;AAEA,UAAMU,aAAN,CAAoBC,KAApB;AAEA,UAAMK,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,SAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,SAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AAEA,SAAKjB,MAAL,GAAc,KAAKD,KAAnB;AACA,SAAKI,MAAL,GAAc,KAAKD,KAAnB;AAEA,SAAKgB,QAAL,CAAcT,KAAd;AACA,SAAKU,UAAL;AAEA,SAAKC,mBAAL,CAAyBX,KAAzB;AACD;;AAESY,EAAAA,YAAY,CAACZ,KAAD,EAA4B;AAChD,SAAKG,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,UAAMY,YAAN,CAAmBZ,KAAnB;AACA,SAAKS,QAAL,CAAcT,KAAd;AAEA,SAAKR,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AACA,SAAKI,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AAEA,UAAMW,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,SAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,SAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AAEA,SAAKjB,MAAL,GAAc,KAAKD,KAAnB;AACA,SAAKI,MAAL,GAAc,KAAKD,KAAnB;;AAEA,QAAI,KAAKU,OAAL,CAAaU,oBAAb,GAAoC,KAAK/C,WAA7C,EAA0D;AACxD,UAAI,KAAKgD,KAAL,KAAexE,KAAK,CAACyE,MAAzB,EAAiC;AAC/B,aAAKC,MAAL;AACD,OAFD,MAEO;AACL,aAAKC,IAAL;AACD;AACF,KAND,MAMO;AACL,WAAKP,UAAL;AACD;AACF;;AAESQ,EAAAA,WAAW,CAAClB,KAAD,EAA4B;AAC/C,SAAKX,UAAL,GAAkBW,KAAK,CAACX,UAAxB;AAEA,UAAM6B,WAAN,CAAkBlB,KAAlB;;AACA,QAAI,KAAKc,KAAL,KAAexE,KAAK,CAACyE,MAAzB,EAAiC;AAC/B,YAAMV,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,WAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,WAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AACD;;AAED,SAAKL,OAAL,CAAagB,iBAAb,CAA+BnB,KAAK,CAACoB,SAArC;;AAEA,QAAI,KAAKjB,OAAL,CAAaU,oBAAb,KAAsC,CAA1C,EAA6C;AAC3C,WAAKjB,sBAAL;AACD;;AAED,QAAI,KAAKkB,KAAL,KAAexE,KAAK,CAACyE,MAAzB,EAAiC;AAC/B,WAAKM,GAAL;AACD,KAFD,MAEO;AACL,WAAKC,aAAL;AACA,WAAKL,IAAL;AACD;AACF;;AAESM,EAAAA,eAAe,CAACvB,KAAD,EAA4B;AACnD,UAAMuB,eAAN,CAAsBvB,KAAtB;AACA,SAAKG,OAAL,CAAagB,iBAAb,CAA+BnB,KAAK,CAACoB,SAArC;AAEA,SAAK5B,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AACA,SAAKI,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AAEA,UAAMW,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,SAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,SAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AAEA,SAAKjB,MAAL,GAAc,KAAKD,KAAnB;AACA,SAAKI,MAAL,GAAc,KAAKD,KAAnB;;AAEA,QACE,EACE,KAAKqB,KAAL,KAAexE,KAAK,CAACyE,MAArB,IACA,KAAKZ,OAAL,CAAaU,oBAAb,GAAoC,KAAKhD,WAF3C,CADF,EAKE;AACA,WAAK6C,UAAL;AACD;AACF;;AAESc,EAAAA,aAAa,CAACxB,KAAD,EAA4B;AACjD,SAAKG,OAAL,CAAasB,KAAb,CAAmBzB,KAAnB;AACA,SAAKX,UAAL,GAAkBW,KAAK,CAACX,UAAxB;AAEA,UAAMgB,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,SAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,SAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AAEA,UAAMkB,QAAQ,GAAG,KAAKvB,OAAL,CAAawB,WAAb,CAAyB3B,KAAK,CAACoB,SAA/B,CAAjB;AACA,SAAKjC,SAAL,GAAiBuC,QAAQ,CAACnB,CAA1B;AACA,SAAKnB,SAAL,GAAiBsC,QAAQ,CAAClB,CAA1B;AAEA,SAAKE,UAAL;AAEA,UAAMc,aAAN,CAAoBxB,KAApB;AACD;;AAES4B,EAAAA,oBAAoB,CAAC5B,KAAD,EAA4B;AACxD,QAAI,KAAK6B,uBAAT,EAAkC;AAChC;AACD;;AAED,SAAK1B,OAAL,CAAasB,KAAb,CAAmBzB,KAAnB;AACA,SAAKX,UAAL,GAAkBW,KAAK,CAACX,UAAxB;AAEA,UAAMgB,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,SAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,SAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AAEA,UAAMkB,QAAQ,GAAG,KAAKvB,OAAL,CAAawB,WAAb,CAAyB3B,KAAK,CAACoB,SAA/B,CAAjB;AACA,SAAKjC,SAAL,GAAiBuC,QAAQ,CAACnB,CAA1B;AACA,SAAKnB,SAAL,GAAiBsC,QAAQ,CAAClB,CAA1B;AAEA,SAAKE,UAAL;;AAEA,QAAI,KAAKI,KAAL,KAAexE,KAAK,CAACyE,MAAzB,EAAiC;AAC/B,YAAMa,oBAAN,CAA2B5B,KAA3B;AACD;AACF;;AAEO8B,EAAAA,gBAAgB,CAAC9B,KAAD,EAAsB;AAC5CH,IAAAA,YAAY,CAAC,KAAKkC,eAAN,CAAZ;AAEA,SAAKA,eAAL,GAAuBC,UAAU,CAAC,MAAM;AACtC,UAAI,KAAKlB,KAAL,KAAexE,KAAK,CAACyE,MAAzB,EAAiC;AAC/B,aAAKM,GAAL;AACA,aAAKlB,OAAL,CAAagB,iBAAb,CAA+BnB,KAAK,CAACoB,SAArC;AACA,aAAKN,KAAL,GAAaxE,KAAK,CAACW,YAAnB;AACD;;AAED,WAAKgF,WAAL,GAAmBzF,WAAW,CAACS,YAA/B;AACD,KARgC,EAQ9B,EAR8B,CAAjC;AASD;;AAESiF,EAAAA,OAAO,CAAClC,KAAD,EAA4B;AAC3C,QACE,KAAKiC,WAAL,KAAqBzF,WAAW,CAAC2F,KAAjC,IACA,CAAC,KAAKxD,8BAFR,EAGE;AACA;AACD;;AAED,QAAI,KAAKmC,KAAL,KAAexE,KAAK,CAACW,YAAzB,EAAuC;AACrC,WAAKgF,WAAL,GACEjC,KAAK,CAACoC,WAAN,GAAqB,GAArB,KAA6B,CAA7B,GACI5F,WAAW,CAAC6F,QADhB,GAEI7F,WAAW,CAAC2F,KAHlB;;AAKA,UAAI,KAAKF,WAAL,KAAqBzF,WAAW,CAAC2F,KAArC,EAA4C;AAC1C,aAAKL,gBAAL,CAAsB9B,KAAtB;AACA;AACD;;AAED,WAAKG,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AAEA,YAAMK,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,WAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,WAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AAEA,WAAKjB,MAAL,GAAc,KAAKD,KAAnB;AACA,WAAKI,MAAL,GAAc,KAAKD,KAAnB;AAEA,WAAK6C,KAAL;AACA,WAAKC,QAAL;AACD;;AACD,SAAKpC,OAAL,CAAasB,KAAb,CAAmBzB,KAAnB;AAEA,UAAMK,UAAU,GAAG,KAAKF,OAAL,CAAaG,wBAAb,EAAnB;AACA,SAAKhB,KAAL,GAAae,UAAU,CAACE,CAAxB;AACA,SAAKd,KAAL,GAAaY,UAAU,CAACG,CAAxB;AAEA,UAAMkB,QAAQ,GAAG,KAAKvB,OAAL,CAAawB,WAAb,CAAyB3B,KAAK,CAACoB,SAA/B,CAAjB;AACA,SAAKjC,SAAL,GAAiBuC,QAAQ,CAACnB,CAA1B;AACA,SAAKnB,SAAL,GAAiBsC,QAAQ,CAAClB,CAA1B;AAEA,SAAKgC,kBAAL,CAAwB,KAAxB,EAA+BxC,KAA/B;AACA,SAAK8B,gBAAL,CAAsB9B,KAAtB;AACD;;AAEOyC,EAAAA,cAAc,GAAY;AAChC,UAAMC,EAAU,GAAG,KAAK3D,eAAL,EAAnB;;AAEA,QACE,KAAKZ,kBAAL,KAA4BrB,MAAM,CAACC,gBAAnC,IACA2F,EAAE,GAAG,KAAKvE,kBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKC,gBAAL,KAA0BtB,MAAM,CAACE,gBAAjC,IACA0F,EAAE,GAAG,KAAKtE,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMuE,EAAU,GAAG,KAAK1D,eAAL,EAAnB;;AAEA,QACE,KAAKV,kBAAL,KAA4BzB,MAAM,CAACC,gBAAnC,IACA4F,EAAE,GAAG,KAAKpE,kBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKC,gBAAL,KAA0B1B,MAAM,CAACE,gBAAjC,IACA2F,EAAE,GAAG,KAAKnE,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMoE,UAAkB,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA1C;;AAEA,QACE,KAAKhF,SAAL,KAAmBb,MAAM,CAACC,gBAA1B,IACA6F,UAAU,IAAI,KAAKjF,SAFrB,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMkF,EAAU,GAAG,KAAK1D,SAAxB;;AAEA,QACE,KAAKnB,YAAL,KAAsBlB,MAAM,CAACC,gBAA7B,KACE,KAAKiB,YAAL,GAAoB,CAApB,IAAyB6E,EAAE,IAAI,KAAK7E,YAArC,IACE,KAAKA,YAAL,IAAqB,CAArB,IAA0B,KAAKA,YAAL,IAAqB6E,EAFlD,CADF,EAIE;AACA,aAAO,IAAP;AACD;;AAED,UAAMC,EAAU,GAAG,KAAK1D,SAAxB;;AACA,QACE,KAAKnB,YAAL,KAAsBnB,MAAM,CAACC,gBAA7B,KACE,KAAKkB,YAAL,GAAoB,CAApB,IAAyB6E,EAAE,IAAI,KAAK7E,YAArC,IACE,KAAKA,YAAL,IAAqB,CAArB,IAA0B,KAAKA,YAAL,IAAqB6E,EAFlD,CADF,EAIE;AACA,aAAO,IAAP;AACD;;AAED,UAAMC,UAAkB,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA1C;AAEA,WACE,KAAKlE,aAAL,KAAuB9B,MAAM,CAACC,gBAA9B,IACAgG,UAAU,IAAI,KAAKnE,aAFrB;AAID;;AAEOoE,EAAAA,UAAU,GAAY;AAC5B,UAAMN,EAAU,GAAG,KAAK3D,eAAL,EAAnB;AACA,UAAM4D,EAAU,GAAG,KAAK1D,eAAL,EAAnB;AACA,UAAM2D,UAAU,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAAlC;;AAEA,QAAI,KAAKzE,sBAAL,GAA8B,CAA9B,IAAmC0E,UAAU,GAAGhG,mBAApD,EAAyE;AACvE,WAAKgD,sBAAL;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKvB,gBAAL,KAA0BvB,MAAM,CAACE,gBAAjC,IACA0F,EAAE,GAAG,KAAKrE,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKC,cAAL,KAAwBxB,MAAM,CAACC,gBAA/B,IACA2F,EAAE,GAAG,KAAKpE,cAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKG,gBAAL,KAA0B3B,MAAM,CAACE,gBAAjC,IACA2F,EAAE,GAAG,KAAKlE,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,WACE,KAAKC,cAAL,KAAwB5B,MAAM,CAACC,gBAA/B,IACA4F,EAAE,GAAG,KAAKjE,cAFZ;AAID;;AAEO+B,EAAAA,QAAQ,CAACT,KAAD,EAA4B;AAC1C,QACE,KAAKc,KAAL,KAAexE,KAAK,CAACW,YAArB,IACA,KAAKkD,OAAL,CAAaU,oBAAb,IAAqC,KAAKhD,WAF5C,EAGE;AACA,WAAKyD,aAAL;AACA,WAAK9B,OAAL,GAAe,CAAf;AACA,WAAKG,OAAL,GAAe,CAAf;AACA,WAAKR,SAAL,GAAiB,CAAjB;AACA,WAAKC,SAAL,GAAiB,CAAjB;AAEA,WAAKkD,KAAL;;AAEA,UAAI,KAAKpE,sBAAL,GAA8B,CAAlC,EAAqC;AACnC,aAAK4B,iBAAL,GAAyBkC,UAAU,CAAC,MAAM;AACxC,eAAKO,QAAL;AACD,SAFkC,EAEhC,KAAKrE,sBAF2B,CAAnC;AAGD;AACF,KAjBD,MAiBO;AACL,YAAMwD,QAAQ,GAAG,KAAKvB,OAAL,CAAawB,WAAb,CAAyB3B,KAAK,CAACoB,SAA/B,CAAjB;AACA,WAAKjC,SAAL,GAAiBuC,QAAQ,CAACnB,CAA1B;AACA,WAAKnB,SAAL,GAAiBsC,QAAQ,CAAClB,CAA1B;AACD;AACF;;AAEOE,EAAAA,UAAU,GAAS;AACzB,QAAI,KAAKI,KAAL,KAAexE,KAAK,CAAC2G,KAAzB,EAAgC;AAC9B,UAAI,KAAKD,UAAL,EAAJ,EAAuB;AACrB,aAAK/B,IAAL;AACD,OAFD,MAEO,IAAI,KAAKwB,cAAL,EAAJ,EAA2B;AAChC,aAAKF,QAAL;AACD;AACF;AACF;;AAEMA,EAAAA,QAAQ,CAACW,KAAK,GAAG,KAAT,EAAsB;AACnC,QAAI,KAAKpC,KAAL,KAAexE,KAAK,CAACyE,MAAzB,EAAiC;AAC/B,WAAKO,aAAL;AACD;;AAED,UAAMiB,QAAN,CAAeW,KAAf;AACD;;AAESC,EAAAA,QAAQ,GAAS;AACzB,SAAKvD,sBAAL;AACD;;AAESwD,EAAAA,OAAO,GAAS;AACxB,SAAKxD,sBAAL;AACD;;AAES0B,EAAAA,aAAa,GAAS;AAC9B,QAAI,KAAKR,KAAL,KAAexE,KAAK,CAACyE,MAAzB,EAAiC;AAC/B;AACD;;AAED,SAAKxB,MAAL,GAAc,KAAKD,KAAnB;AACA,SAAKI,MAAL,GAAc,KAAKD,KAAnB;AACD;;AA3jB2D", "sourcesContent": ["import { State } from '../../State';\nimport { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent, Config, StylusData, WheelDevice } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\n\nconst DEFAULT_MIN_POINTERS = 1;\nconst DEFAULT_MAX_POINTERS = 10;\nconst DEFAULT_MIN_DIST_SQ = DEFAULT_TOUCH_SLOP * DEFAULT_TOUCH_SLOP;\n\nexport default class PanGestureHandler extends GestureHandler {\n  private readonly customActivationProperties: string[] = [\n    'activeOffsetXStart',\n    'activeOffsetXEnd',\n    'failOffsetXStart',\n    'failOffsetXEnd',\n    'activeOffsetYStart',\n    'activeOffsetYEnd',\n    'failOffsetYStart',\n    'failOffsetYEnd',\n    'minVelocityX',\n    'minVelocityY',\n    'minVelocity',\n  ];\n\n  public velocityX = 0;\n  public velocityY = 0;\n\n  private minDistSq = DEFAULT_MIN_DIST_SQ;\n\n  private activeOffsetXStart = -Number.MAX_SAFE_INTEGER;\n  private activeOffsetXEnd = Number.MIN_SAFE_INTEGER;\n  private failOffsetXStart = Number.MIN_SAFE_INTEGER;\n  private failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n\n  private activeOffsetYStart = Number.MAX_SAFE_INTEGER;\n  private activeOffsetYEnd = Number.MIN_SAFE_INTEGER;\n  private failOffsetYStart = Number.MIN_SAFE_INTEGER;\n  private failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n\n  private minVelocityX = Number.MAX_SAFE_INTEGER;\n  private minVelocityY = Number.MAX_SAFE_INTEGER;\n  private minVelocitySq = Number.MAX_SAFE_INTEGER;\n\n  private minPointers = DEFAULT_MIN_POINTERS;\n  private maxPointers = DEFAULT_MAX_POINTERS;\n\n  private startX = 0;\n  private startY = 0;\n  private offsetX = 0;\n  private offsetY = 0;\n  private lastX = 0;\n  private lastY = 0;\n\n  private stylusData: StylusData | undefined;\n\n  private activateAfterLongPress = 0;\n  private activationTimeout = 0;\n\n  private enableTrackpadTwoFingerGesture = false;\n  private endWheelTimeout = 0;\n  private wheelDevice = WheelDevice.UNDETERMINED;\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    this.resetConfig();\n\n    super.updateGestureConfig({ enabled: enabled, ...props });\n    this.checkCustomActivationCriteria(this.customActivationProperties);\n\n    if (this.config.minDist !== undefined) {\n      this.minDistSq = this.config.minDist * this.config.minDist;\n    } else if (this.hasCustomActivationCriteria) {\n      this.minDistSq = Number.MAX_SAFE_INTEGER;\n    }\n\n    if (this.config.minPointers !== undefined) {\n      this.minPointers = this.config.minPointers;\n    }\n\n    if (this.config.maxPointers !== undefined) {\n      this.maxPointers = this.config.maxPointers;\n    }\n\n    if (this.config.minVelocity !== undefined) {\n      this.minVelocityX = this.config.minVelocity;\n      this.minVelocityY = this.config.minVelocity;\n    }\n\n    if (this.config.minVelocityX !== undefined) {\n      this.minVelocityX = this.config.minVelocityX;\n    }\n\n    if (this.config.minVelocityY !== undefined) {\n      this.minVelocityY = this.config.minVelocityY;\n    }\n\n    if (this.config.activateAfterLongPress !== undefined) {\n      this.activateAfterLongPress = this.config.activateAfterLongPress;\n    }\n\n    if (this.config.activeOffsetXStart !== undefined) {\n      this.activeOffsetXStart = this.config.activeOffsetXStart;\n\n      if (this.config.activeOffsetXEnd === undefined) {\n        this.activeOffsetXEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.activeOffsetXEnd !== undefined) {\n      this.activeOffsetXEnd = this.config.activeOffsetXEnd;\n\n      if (this.config.activeOffsetXStart === undefined) {\n        this.activeOffsetXStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetXStart !== undefined) {\n      this.failOffsetXStart = this.config.failOffsetXStart;\n\n      if (this.config.failOffsetXEnd === undefined) {\n        this.failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetXEnd !== undefined) {\n      this.failOffsetXEnd = this.config.failOffsetXEnd;\n\n      if (this.config.failOffsetXStart === undefined) {\n        this.failOffsetXStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.activeOffsetYStart !== undefined) {\n      this.activeOffsetYStart = this.config.activeOffsetYStart;\n\n      if (this.config.activeOffsetYEnd === undefined) {\n        this.activeOffsetYEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.activeOffsetYEnd !== undefined) {\n      this.activeOffsetYEnd = this.config.activeOffsetYEnd;\n\n      if (this.config.activeOffsetYStart === undefined) {\n        this.activeOffsetYStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetYStart !== undefined) {\n      this.failOffsetYStart = this.config.failOffsetYStart;\n\n      if (this.config.failOffsetYEnd === undefined) {\n        this.failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetYEnd !== undefined) {\n      this.failOffsetYEnd = this.config.failOffsetYEnd;\n\n      if (this.config.failOffsetYStart === undefined) {\n        this.failOffsetYStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.enableTrackpadTwoFingerGesture !== undefined) {\n      this.enableTrackpadTwoFingerGesture =\n        this.config.enableTrackpadTwoFingerGesture;\n    }\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n\n    this.activeOffsetXStart = -Number.MAX_SAFE_INTEGER;\n    this.activeOffsetXEnd = Number.MIN_SAFE_INTEGER;\n    this.failOffsetXStart = Number.MIN_SAFE_INTEGER;\n    this.failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n\n    this.activeOffsetYStart = Number.MAX_SAFE_INTEGER;\n    this.activeOffsetYEnd = Number.MIN_SAFE_INTEGER;\n    this.failOffsetYStart = Number.MIN_SAFE_INTEGER;\n    this.failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n\n    this.minVelocityX = Number.MAX_SAFE_INTEGER;\n    this.minVelocityY = Number.MAX_SAFE_INTEGER;\n    this.minVelocitySq = Number.MAX_SAFE_INTEGER;\n\n    this.minDistSq = DEFAULT_MIN_DIST_SQ;\n\n    this.minPointers = DEFAULT_MIN_POINTERS;\n    this.maxPointers = DEFAULT_MAX_POINTERS;\n\n    this.activateAfterLongPress = 0;\n  }\n\n  protected transformNativeEvent() {\n    const translationX: number = this.getTranslationX();\n    const translationY: number = this.getTranslationY();\n\n    return {\n      ...super.transformNativeEvent(),\n      translationX: isNaN(translationX) ? 0 : translationX,\n      translationY: isNaN(translationY) ? 0 : translationY,\n      velocityX: this.velocityX,\n      velocityY: this.velocityY,\n      stylusData: this.stylusData,\n    };\n  }\n\n  private getTranslationX(): number {\n    return this.lastX - this.startX + this.offsetX;\n  }\n  private getTranslationY(): number {\n    return this.lastY - this.startY + this.offsetY;\n  }\n\n  private clearActivationTimeout(): void {\n    clearTimeout(this.activationTimeout);\n  }\n\n  // Events Handling\n  protected onPointerDown(event: AdaptedEvent): void {\n    if (!this.isButtonInConfig(event.button)) {\n      return;\n    }\n\n    this.tracker.addToTracker(event);\n    this.stylusData = event.stylusData;\n\n    super.onPointerDown(event);\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n\n    this.tryBegin(event);\n    this.checkBegan();\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.tryBegin(event);\n\n    this.offsetX += this.lastX - this.startX;\n    this.offsetY += this.lastY - this.startY;\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n\n    if (this.tracker.trackedPointersCount > this.maxPointers) {\n      if (this.state === State.ACTIVE) {\n        this.cancel();\n      } else {\n        this.fail();\n      }\n    } else {\n      this.checkBegan();\n    }\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    this.stylusData = event.stylusData;\n\n    super.onPointerUp(event);\n    if (this.state === State.ACTIVE) {\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n    }\n\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (this.tracker.trackedPointersCount === 0) {\n      this.clearActivationTimeout();\n    }\n\n    if (this.state === State.ACTIVE) {\n      this.end();\n    } else {\n      this.resetProgress();\n      this.fail();\n    }\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    this.offsetX += this.lastX - this.startX;\n    this.offsetY += this.lastY - this.startY;\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n\n    if (\n      !(\n        this.state === State.ACTIVE &&\n        this.tracker.trackedPointersCount < this.minPointers\n      )\n    ) {\n      this.checkBegan();\n    }\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    this.stylusData = event.stylusData;\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    const velocity = this.tracker.getVelocity(event.pointerId);\n    this.velocityX = velocity.x;\n    this.velocityY = velocity.y;\n\n    this.checkBegan();\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    if (this.shouldCancelWhenOutside) {\n      return;\n    }\n\n    this.tracker.track(event);\n    this.stylusData = event.stylusData;\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    const velocity = this.tracker.getVelocity(event.pointerId);\n    this.velocityX = velocity.x;\n    this.velocityY = velocity.y;\n\n    this.checkBegan();\n\n    if (this.state === State.ACTIVE) {\n      super.onPointerOutOfBounds(event);\n    }\n  }\n\n  private scheduleWheelEnd(event: AdaptedEvent) {\n    clearTimeout(this.endWheelTimeout);\n\n    this.endWheelTimeout = setTimeout(() => {\n      if (this.state === State.ACTIVE) {\n        this.end();\n        this.tracker.removeFromTracker(event.pointerId);\n        this.state = State.UNDETERMINED;\n      }\n\n      this.wheelDevice = WheelDevice.UNDETERMINED;\n    }, 30);\n  }\n\n  protected onWheel(event: AdaptedEvent): void {\n    if (\n      this.wheelDevice === WheelDevice.MOUSE ||\n      !this.enableTrackpadTwoFingerGesture\n    ) {\n      return;\n    }\n\n    if (this.state === State.UNDETERMINED) {\n      this.wheelDevice =\n        event.wheelDeltaY! % 120 !== 0\n          ? WheelDevice.TOUCHPAD\n          : WheelDevice.MOUSE;\n\n      if (this.wheelDevice === WheelDevice.MOUSE) {\n        this.scheduleWheelEnd(event);\n        return;\n      }\n\n      this.tracker.addToTracker(event);\n\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n\n      this.startX = this.lastX;\n      this.startY = this.lastY;\n\n      this.begin();\n      this.activate();\n    }\n    this.tracker.track(event);\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    const velocity = this.tracker.getVelocity(event.pointerId);\n    this.velocityX = velocity.x;\n    this.velocityY = velocity.y;\n\n    this.tryToSendMoveEvent(false, event);\n    this.scheduleWheelEnd(event);\n  }\n\n  private shouldActivate(): boolean {\n    const dx: number = this.getTranslationX();\n\n    if (\n      this.activeOffsetXStart !== Number.MAX_SAFE_INTEGER &&\n      dx < this.activeOffsetXStart\n    ) {\n      return true;\n    }\n\n    if (\n      this.activeOffsetXEnd !== Number.MIN_SAFE_INTEGER &&\n      dx > this.activeOffsetXEnd\n    ) {\n      return true;\n    }\n\n    const dy: number = this.getTranslationY();\n\n    if (\n      this.activeOffsetYStart !== Number.MAX_SAFE_INTEGER &&\n      dy < this.activeOffsetYStart\n    ) {\n      return true;\n    }\n\n    if (\n      this.activeOffsetYEnd !== Number.MIN_SAFE_INTEGER &&\n      dy > this.activeOffsetYEnd\n    ) {\n      return true;\n    }\n\n    const distanceSq: number = dx * dx + dy * dy;\n\n    if (\n      this.minDistSq !== Number.MAX_SAFE_INTEGER &&\n      distanceSq >= this.minDistSq\n    ) {\n      return true;\n    }\n\n    const vx: number = this.velocityX;\n\n    if (\n      this.minVelocityX !== Number.MAX_SAFE_INTEGER &&\n      ((this.minVelocityX < 0 && vx <= this.minVelocityX) ||\n        (this.minVelocityX >= 0 && this.minVelocityX <= vx))\n    ) {\n      return true;\n    }\n\n    const vy: number = this.velocityY;\n    if (\n      this.minVelocityY !== Number.MAX_SAFE_INTEGER &&\n      ((this.minVelocityY < 0 && vy <= this.minVelocityY) ||\n        (this.minVelocityY >= 0 && this.minVelocityY <= vy))\n    ) {\n      return true;\n    }\n\n    const velocitySq: number = vx * vx + vy * vy;\n\n    return (\n      this.minVelocitySq !== Number.MAX_SAFE_INTEGER &&\n      velocitySq >= this.minVelocitySq\n    );\n  }\n\n  private shouldFail(): boolean {\n    const dx: number = this.getTranslationX();\n    const dy: number = this.getTranslationY();\n    const distanceSq = dx * dx + dy * dy;\n\n    if (this.activateAfterLongPress > 0 && distanceSq > DEFAULT_MIN_DIST_SQ) {\n      this.clearActivationTimeout();\n      return true;\n    }\n\n    if (\n      this.failOffsetXStart !== Number.MIN_SAFE_INTEGER &&\n      dx < this.failOffsetXStart\n    ) {\n      return true;\n    }\n\n    if (\n      this.failOffsetXEnd !== Number.MAX_SAFE_INTEGER &&\n      dx > this.failOffsetXEnd\n    ) {\n      return true;\n    }\n\n    if (\n      this.failOffsetYStart !== Number.MIN_SAFE_INTEGER &&\n      dy < this.failOffsetYStart\n    ) {\n      return true;\n    }\n\n    return (\n      this.failOffsetYEnd !== Number.MAX_SAFE_INTEGER &&\n      dy > this.failOffsetYEnd\n    );\n  }\n\n  private tryBegin(event: AdaptedEvent): void {\n    if (\n      this.state === State.UNDETERMINED &&\n      this.tracker.trackedPointersCount >= this.minPointers\n    ) {\n      this.resetProgress();\n      this.offsetX = 0;\n      this.offsetY = 0;\n      this.velocityX = 0;\n      this.velocityY = 0;\n\n      this.begin();\n\n      if (this.activateAfterLongPress > 0) {\n        this.activationTimeout = setTimeout(() => {\n          this.activate();\n        }, this.activateAfterLongPress);\n      }\n    } else {\n      const velocity = this.tracker.getVelocity(event.pointerId);\n      this.velocityX = velocity.x;\n      this.velocityY = velocity.y;\n    }\n  }\n\n  private checkBegan(): void {\n    if (this.state === State.BEGAN) {\n      if (this.shouldFail()) {\n        this.fail();\n      } else if (this.shouldActivate()) {\n        this.activate();\n      }\n    }\n  }\n\n  public activate(force = false): void {\n    if (this.state !== State.ACTIVE) {\n      this.resetProgress();\n    }\n\n    super.activate(force);\n  }\n\n  protected onCancel(): void {\n    this.clearActivationTimeout();\n  }\n\n  protected onReset(): void {\n    this.clearActivationTimeout();\n  }\n\n  protected resetProgress(): void {\n    if (this.state === State.ACTIVE) {\n      return;\n    }\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n  }\n}\n"]}