{"version": 3, "sources": ["TapGestureHandler.ts"], "names": ["Hammer", "DiscreteGestureHandler", "isnan", "TapGestureHandler", "ev", "_getPendingGestures", "length", "_shouldFireEndEvent", "eventType", "INPUT_END", "sendEvent", "INPUT_MOVE", "isFinal", "onGestureEnded", "name", "NativeGestureClass", "Tap", "max<PERSON>elay<PERSON>", "config", "simulateCancelEvent", "inputData", "isGestureRunning", "cancelEvent", "onGestureActivated", "onSuccessfulTap", "onRawEvent", "hasGestureFailed", "gesture", "hammer", "get", "options", "enable", "clearTimeout", "_multiTapTimer", "onStart", "maxPointers", "setTimeout", "_timer", "getHammerConfig", "event", "taps", "numberOfTaps", "interval", "time", "maxDurationMs", "updateGestureConfig", "shouldCancelWhenOutside", "maxDeltaX", "Number", "NaN", "maxDeltaY", "minDurationMs", "maxDist", "minPointers", "props", "onWaitingEnded", "_gesture"], "mappings": ";;AAAA,OAAOA,MAAP,MAAmB,gBAAnB;AAEA,OAAOC,sBAAP,MAAmC,0BAAnC;AAEA,SAASC,KAAT,QAAsB,SAAtB;;AAEA,MAAMC,iBAAN,SAAgCF,sBAAhC,CAAuD;AAAA;AAAA;;AAAA,iDACA,IADA;;AAAA;;AAAA;;AAAA,6CA6BlCG,EAAD,IAAwB;AACxC,UAAI,KAAKC,mBAAL,GAA2BC,MAA/B,EAAuC;AACrC,aAAKC,mBAAL,GAA2BH,EAA3B;AACA;AACD;;AACD,UAAIA,EAAE,CAACI,SAAH,KAAiBR,MAAM,CAACS,SAA5B,EAAuC;AACrC,aAAKC,SAAL,CAAe,EAAE,GAAGN,EAAL;AAASI,UAAAA,SAAS,EAAER,MAAM,CAACW;AAA3B,SAAf;AACD,OAPuC,CAQxC;;;AACA,WAAKD,SAAL,CAAe,EAAE,GAAGN,EAAL;AAASQ,QAAAA,OAAO,EAAE;AAAlB,OAAf;AACA,WAAKC,cAAL,CAAoBT,EAApB;AACD,KAxCoD;AAAA;;AAGxB;AACrB,MAAJU,IAAI,GAAG;AACT,WAAO,KAAP;AACD;;AAEqB,MAAlBC,kBAAkB,GAAG;AACvB,WAAOf,MAAM,CAACgB,GAAd;AACD;;AAEa,MAAVC,UAAU,GAAG;AACf;AACA,WAAOf,KAAK,CAAC,KAAKgB,MAAL,CAAYD,UAAb,CAAL,GAAgC,GAAhC,GAAsC,KAAKC,MAAL,CAAYD,UAAzD;AACD;;AAEDE,EAAAA,mBAAmB,CAACC,SAAD,EAA4B;AAC7C,QAAI,KAAKC,gBAAT,EAA2B;AACzB,WAAKC,WAAL,CAAiBF,SAAjB;AACD;AACF;;AAEDG,EAAAA,kBAAkB,CAACnB,EAAD,EAAqB;AACrC,QAAI,KAAKiB,gBAAT,EAA2B;AACzB,WAAKG,eAAL,CAAqBpB,EAArB;AACD;AACF;;AAeDqB,EAAAA,UAAU,CAACrB,EAAD,EAAkB;AAC1B,UAAMqB,UAAN,CAAiBrB,EAAjB,EAD0B,CAG1B;;AACA,QACE,CAAC,KAAKsB,gBAAN,IACA,CAAC,KAAKL,gBADN,IAEA;AACA,KAACjB,EAAE,CAACQ,OAJN,EAKE;AACA;AACA,YAAMe,OAAO,GAAG,KAAKC,MAAL,CAAaC,GAAb,CAAiB,KAAKf,IAAtB,CAAhB,CAFA,CAGA;;AACA,UAAIa,OAAO,CAACG,OAAR,CAAgBC,MAAhB,CAAuBJ,OAAvB,EAAgCvB,EAAhC,CAAJ,EAAyC;AACvC4B,QAAAA,YAAY,CAAC,KAAKC,cAAN,CAAZ;AAEA,aAAKC,OAAL,CAAa9B,EAAb;AACA,aAAKM,SAAL,CAAeN,EAAf;AACD;AACF;;AACD,QAAIA,EAAE,CAACQ,OAAH,IAAcR,EAAE,CAAC+B,WAAH,GAAiB,CAAnC,EAAsC;AACpCC,MAAAA,UAAU,CAAC,MAAM;AACf;AACA;AACA,YAAI,KAAKf,gBAAT,EAA2B;AACzB,eAAKC,WAAL,CAAiBlB,EAAjB;AACD;AACF,OANS,CAAV;AAOD;;AAED,QAAI,KAAKsB,gBAAT,EAA2B;AACzB;AACD,KAhCyB,CAiC1B;AACA;;;AACA,QAAItB,EAAE,CAACQ,OAAP,EAAgB;AACd;AACA;AACA,UAAIR,EAAE,CAAC+B,WAAH,GAAiB,CAArB,EAAwB;AACtBC,QAAAA,UAAU,CAAC,MAAM;AACf,cAAI,KAAKf,gBAAT,EAA2B;AACzB,iBAAKC,WAAL,CAAiBlB,EAAjB;AACD;AACF,SAJS,CAAV;AAKD,OATa,CAWd;;;AACA4B,MAAAA,YAAY,CAAC,KAAKK,MAAN,CAAZ,CAZc,CAad;;AACA,WAAKA,MAAL,GAAcD,UAAU,CAAC,MAAM;AAC7B,aAAKV,gBAAL,GAAwB,IAAxB;AACA,aAAKJ,WAAL,CAAiBlB,EAAjB;AACD,OAHuB,EAGrB,KAAKa,UAHgB,CAAxB;AAID,KAlBD,MAkBO,IAAI,CAAC,KAAKS,gBAAN,IAA0B,CAAC,KAAKL,gBAApC,EAAsD;AAC3D;AACA,YAAMM,OAAO,GAAG,KAAKC,MAAL,CAAaC,GAAb,CAAiB,KAAKf,IAAtB,CAAhB,CAF2D,CAG3D;;AACA,UAAIa,OAAO,CAACG,OAAR,CAAgBC,MAAhB,CAAuBJ,OAAvB,EAAgCvB,EAAhC,CAAJ,EAAyC;AACvC4B,QAAAA,YAAY,CAAC,KAAKC,cAAN,CAAZ;AAEA,aAAKC,OAAL,CAAa9B,EAAb;AACA,aAAKM,SAAL,CAAeN,EAAf;AACD;AACF;AACF;;AAEDkC,EAAAA,eAAe,GAAG;AAChB,WAAO,EACL,GAAG,MAAMA,eAAN,EADE;AAELC,MAAAA,KAAK,EAAE,KAAKzB,IAFP;AAGL;AACA0B,MAAAA,IAAI,EAAEtC,KAAK,CAAC,KAAKgB,MAAL,CAAYuB,YAAb,CAAL,GAAkC,CAAlC,GAAsC,KAAKvB,MAAL,CAAYuB,YAJnD;AAKLC,MAAAA,QAAQ,EAAE,KAAKzB,UALV;AAML0B,MAAAA,IAAI,EACF;AACAzC,MAAAA,KAAK,CAAC,KAAKgB,MAAL,CAAY0B,aAAb,CAAL,IAAoC,KAAK1B,MAAL,CAAY0B,aAAZ,IAA6B,IAAjE,GACI,GADJ,GAEI;AACA,WAAK1B,MAAL,CAAY0B;AAXb,KAAP;AAaD;;AAEDC,EAAAA,mBAAmB,CAAC;AAClBC,IAAAA,uBAAuB,GAAG,IADR;AAElBC,IAAAA,SAAS,GAAGC,MAAM,CAACC,GAFD;AAGlBC,IAAAA,SAAS,GAAGF,MAAM,CAACC,GAHD;AAIlBR,IAAAA,YAAY,GAAG,CAJG;AAKlBU,IAAAA,aAAa,GAAG,GALE;AAMlBlC,IAAAA,UAAU,GAAG+B,MAAM,CAACC,GANF;AAOlB;AACAL,IAAAA,aAAa,GAAGI,MAAM,CAACC,GARL;AASlBG,IAAAA,OAAO,GAAG,CATQ;AAUlBC,IAAAA,WAAW,GAAG,CAVI;AAWlBlB,IAAAA,WAAW,GAAG,CAXI;AAYlB,OAAGmB;AAZe,GAAD,EAahB;AACD,WAAO,MAAMT,mBAAN,CAA0B;AAC/BC,MAAAA,uBAD+B;AAE/BL,MAAAA,YAF+B;AAG/BM,MAAAA,SAH+B;AAI/BG,MAAAA,SAJ+B;AAK/BC,MAAAA,aAL+B;AAM/BlC,MAAAA,UAN+B;AAO/BmC,MAAAA,OAP+B;AAQ/BC,MAAAA,WAR+B;AAS/BlB,MAAAA,WAT+B;AAU/B,SAAGmB;AAV4B,KAA1B,CAAP;AAYD;;AAEDzC,EAAAA,cAAc,CAAC,GAAGyC,KAAJ,EAAgB;AAC5BtB,IAAAA,YAAY,CAAC,KAAKK,MAAN,CAAZ,CAD4B,CAE5B;;AACA,UAAMxB,cAAN,CAAqB,GAAGyC,KAAxB;AACD;;AAEDC,EAAAA,cAAc,CAACC,QAAD,EAAgB;AAC5B,QAAI,KAAKjD,mBAAT,EAA8B;AAC5B,WAAKiB,eAAL,CAAqB,KAAKjB,mBAA1B;AACA,WAAKA,mBAAL,GAA2B,IAA3B;AACD;AACF;;AAnKoD;;AAqKvD,eAAeJ,iBAAf", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\n\nimport DiscreteGestureHandler from './DiscreteGestureHandler';\nimport { HammerInputExt } from './GestureHandler';\nimport { isnan } from './utils';\n\nclass TapGestureHandler extends DiscreteGestureHandler {\n  private _shouldFireEndEvent: HammerInputExt | null = null;\n  private _timer: any;\n  private _multiTapTimer: any; // TODO unused?\n  get name() {\n    return 'tap';\n  }\n\n  get NativeGestureClass() {\n    return Hammer.Tap;\n  }\n\n  get maxDelayMs() {\n    // @ts-ignore TODO(TS) trace down config\n    return isnan(this.config.maxDelayMs) ? 300 : this.config.maxDelayMs;\n  }\n\n  simulateCancelEvent(inputData: HammerInputExt) {\n    if (this.isGestureRunning) {\n      this.cancelEvent(inputData);\n    }\n  }\n\n  onGestureActivated(ev: HammerInputExt) {\n    if (this.isGestureRunning) {\n      this.onSuccessfulTap(ev);\n    }\n  }\n\n  onSuccessfulTap = (ev: HammerInputExt) => {\n    if (this._getPendingGestures().length) {\n      this._shouldFireEndEvent = ev;\n      return;\n    }\n    if (ev.eventType === Hammer.INPUT_END) {\n      this.sendEvent({ ...ev, eventType: Hammer.INPUT_MOVE });\n    }\n    // When handler gets activated it will turn into State.END immediately.\n    this.sendEvent({ ...ev, isFinal: true });\n    this.onGestureEnded(ev);\n  };\n\n  onRawEvent(ev: HammerInput) {\n    super.onRawEvent(ev);\n\n    // Attempt to create a touch-down event by checking if a valid tap hasn't started yet, then validating the input.\n    if (\n      !this.hasGestureFailed &&\n      !this.isGestureRunning &&\n      // Prevent multi-pointer events from misfiring.\n      !ev.isFinal\n    ) {\n      // Tap Gesture start event\n      const gesture = this.hammer!.get(this.name);\n      // @ts-ignore TODO(TS) trace down config\n      if (gesture.options.enable(gesture, ev)) {\n        clearTimeout(this._multiTapTimer);\n\n        this.onStart(ev);\n        this.sendEvent(ev);\n      }\n    }\n    if (ev.isFinal && ev.maxPointers > 1) {\n      setTimeout(() => {\n        // Handle case where one finger presses slightly\n        // after the first finger on a multi-tap event\n        if (this.isGestureRunning) {\n          this.cancelEvent(ev);\n        }\n      });\n    }\n\n    if (this.hasGestureFailed) {\n      return;\n    }\n    // Hammer doesn't send a `cancel` event for taps.\n    // Manually fail the event.\n    if (ev.isFinal) {\n      // Handle case where one finger presses slightly\n      // after the first finger on a multi-tap event\n      if (ev.maxPointers > 1) {\n        setTimeout(() => {\n          if (this.isGestureRunning) {\n            this.cancelEvent(ev);\n          }\n        });\n      }\n\n      // Clear last timer\n      clearTimeout(this._timer);\n      // Create time out for multi-taps.\n      this._timer = setTimeout(() => {\n        this.hasGestureFailed = true;\n        this.cancelEvent(ev);\n      }, this.maxDelayMs);\n    } else if (!this.hasGestureFailed && !this.isGestureRunning) {\n      // Tap Gesture start event\n      const gesture = this.hammer!.get(this.name);\n      // @ts-ignore TODO(TS) trace down config\n      if (gesture.options.enable(gesture, ev)) {\n        clearTimeout(this._multiTapTimer);\n\n        this.onStart(ev);\n        this.sendEvent(ev);\n      }\n    }\n  }\n\n  getHammerConfig() {\n    return {\n      ...super.getHammerConfig(),\n      event: this.name,\n      // @ts-ignore TODO(TS) trace down config\n      taps: isnan(this.config.numberOfTaps) ? 1 : this.config.numberOfTaps,\n      interval: this.maxDelayMs,\n      time:\n        // @ts-ignore TODO(TS) trace down config\n        isnan(this.config.maxDurationMs) || this.config.maxDurationMs == null\n          ? 250\n          : // @ts-ignore TODO(TS) trace down config\n            this.config.maxDurationMs,\n    };\n  }\n\n  updateGestureConfig({\n    shouldCancelWhenOutside = true,\n    maxDeltaX = Number.NaN,\n    maxDeltaY = Number.NaN,\n    numberOfTaps = 1,\n    minDurationMs = 525,\n    maxDelayMs = Number.NaN,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- TODO possibly forgotten to use in updateGestureConfig?\n    maxDurationMs = Number.NaN,\n    maxDist = 2,\n    minPointers = 1,\n    maxPointers = 1,\n    ...props\n  }) {\n    return super.updateGestureConfig({\n      shouldCancelWhenOutside,\n      numberOfTaps,\n      maxDeltaX,\n      maxDeltaY,\n      minDurationMs,\n      maxDelayMs,\n      maxDist,\n      minPointers,\n      maxPointers,\n      ...props,\n    });\n  }\n\n  onGestureEnded(...props: any) {\n    clearTimeout(this._timer);\n    // @ts-ignore TODO(TS) check how onGestureEnded works\n    super.onGestureEnded(...props);\n  }\n\n  onWaitingEnded(_gesture: any) {\n    if (this._shouldFireEndEvent) {\n      this.onSuccessfulTap(this._shouldFireEndEvent);\n      this._shouldFireEndEvent = null;\n    }\n  }\n}\nexport default TapGestureHandler;\n"]}