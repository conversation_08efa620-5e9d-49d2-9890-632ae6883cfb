import React, { useState, useEffect, useRef } from 'react';
import {
  View, Text, TouchableOpacity, StatusBar, ActivityIndicator, Alert, StyleSheet
} from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import * as ScreenOrientation from 'expo-screen-orientation';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, runOnJS } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import soraApi from '../services/soraApi';

// Helper to format time from seconds into HH:MM:SS or MM:SS
const formatTime = (seconds = 0) => {
    const s = Math.floor(Math.max(0, seconds));
    const m = Math.floor(s / 60);
    const h = Math.floor(m / 60);
    if (h > 0) return `${h}:${(m % 60).toString().padStart(2, '0')}:${(s % 60).toString().padStart(2, '0')}`;
    return `${m}:${(s % 60).toString().padStart(2, '0')}`;
};

// Custom Slider Component - now takes status as a prop
const CustomSlider = ({ player, status, width }) => {
  const isScrubbing = useSharedValue(false);
  const knobPosition = useSharedValue(0);

  const onGestureEvent = (event) => {
    isScrubbing.value = true;
    const newPosition = Math.min(width, Math.max(0, event.x));
    knobPosition.value = newPosition;
  };
  
  const onEndEvent = (event) => {
    if(isScrubbing.value && status.duration > 0) {
      const seekTime = (knobPosition.value / width) * status.duration;
      runOnJS(player.seek)(seekTime);
    }
    isScrubbing.value = false;
  }

  const pan = Gesture.Pan()
    .onBegin(onGestureEvent)
    .onUpdate(onGestureEvent)
    .onEnd(onEndEvent);
  
  useEffect(() => {
    if (!isScrubbing.value && status.duration > 0) {
      knobPosition.value = withTiming((status.position / status.duration) * width, { duration: 100 });
    }
  }, [status.position, status.duration, width, isScrubbing.value]);

  const progressStyle = useAnimatedStyle(() => ({ width: knobPosition.value }));
  const thumbStyle = useAnimatedStyle(() => ({ left: knobPosition.value - 8 }));

  return (
    <View style={{ flex: 1, marginHorizontal: 10 }}>
        <GestureDetector gesture={pan}>
          <View style={slothStyles.sliderContainer}>
            <View style={slothStyles.sliderTrack} />
            <Animated.View style={[slothStyles.sliderProgress, progressStyle]} />
            <Animated.View style={[slothStyles.sliderThumb, thumbStyle]} />
          </View>
        </GestureDetector>
    </View>
  );
};

// The Definitive Player Screen Architecture
const PlayerScreen = ({ route, navigation }) => {
  // --- CORE FIX 1: Stable Player Instance ---
  const playerRef = useRef(useVideoPlayer(null, p => p.play()));
  const player = playerRef.current;

  // --- CORE FIX 2: Correct State Management ---
  const [status, setStatus] = useState({});
  const { isPlaying, isBuffering, duration, position } = status;
  
  const { item } = route.params;
  
  const [streamUrl, setStreamUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLandscape, setIsLandscape] = useState(false);
  const [sliderWidth, setSliderWidth] = useState(0);

  const controlsOpacity = useSharedValue(0);
  const seekIndicatorOpacity = useSharedValue(0);
  const [seekIcon, setSeekIcon] = useState('play');
  
  const controlsTimeout = useRef(null);
  const insets = useSafeAreaInsets();

  useFocusEffect(
    React.useCallback(() => {
      ScreenOrientation.unlockAsync();
      const sub = ScreenOrientation.addOrientationChangeListener(e => setIsLandscape(e.orientationInfo.orientation > 2));
      return () => {
        if (player) player.pause();
        ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
        sub.remove();
      };
    }, [player])
  );
  
  // This useEffect now correctly sets up and tears down the listener
  useEffect(() => {
    if (!player) return;
    const sub = player.addStatusListener(s => setStatus(s || {}));
    return () => sub.remove();
  }, [player]);

  useEffect(() => {
    if (!isPlaying) showControls(true);
    else hideControls();
  }, [isPlaying]);

  useEffect(() => {
    loadStreamData();
  }, [item.id]);

  const loadStreamData = async () => {
    setIsLoading(true);
    try {
      const data = item.media_type === 'tv'
        ? await soraApi.getTVStreams(item.id, item.season, item.episode)
        : await soraApi.getMovieStreams(item.id);
        
      const processed = soraApi.processStreamResponse(data);
      const bestStream = soraApi.getBestQualityStream(processed);

      if (bestStream?.url) {
        player.replace(bestStream.url);
        setStreamUrl(bestStream.url);
        showControls();
      } else {
        throw new Error('No playable stream found');
      }
    } catch (error) {
      Alert.alert('Playback Error', 'Unable to load video stream.', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const showControls = (permanent = false) => {
    clearTimeout(controlsTimeout.current);
    controlsOpacity.value = withTiming(1);
    if (!permanent && isPlaying) hideControls();
  };

  const hideControls = () => {
    clearTimeout(controlsTimeout.current);
    controlsTimeout.current = setTimeout(() => { controlsOpacity.value = withTiming(0); }, 3000);
  };
  
  const togglePlayPause = () => isPlaying ? player.pause() : player.play();
  
  const showSeekIndicator = (iconName) => {
    setSeekIcon(iconName);
    seekIndicatorOpacity.value = withTiming(1, { duration: 150 }, () => {
        seekIndicatorOpacity.value = withDelay(400, withTiming(0));
    });
    showControls();
  };
  
  const toggleFullscreen = () => isLandscape ? ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP) : ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE_RIGHT);

  const singleTap = Gesture.Tap().maxDuration(250).onStart(() => { controlsOpacity.value > 0 ? (controlsOpacity.value = withTiming(0)) : runOnJS(showControls)(); });
  const doubleTapPlayPause = Gesture.Tap().numberOfTaps(2).onStart(() => { runOnJS(showSeekIndicator)(isPlaying ? 'pause' : 'play'); runOnJS(togglePlayPause)(); });
  const doubleTapSeek = (forward) => Gesture.Tap().numberOfTaps(2).onStart(() => { const t = position + (forward ? 10 : -10); player.seek(t); runOnJS(showSeekIndicator)(forward ? 'forward-10' : 'replay-10'); });

  const controlsStyle = useAnimatedStyle(() => ({ opacity: controlsOpacity.value }));
  const seekIndicatorStyle = useAnimatedStyle(() => ({ opacity: seekIndicatorOpacity.value }));
  
  if (isLoading && !streamUrl) {
    return <View style={slothStyles.loadingContainer}><ActivityIndicator color={SLOTH_COLORS.primary} size="large" /></View>;
  }

  return (
    <GestureHandlerRootView style={slothStyles.playerContainer}>
      <StatusBar hidden={isLandscape} />

      {streamUrl ? <VideoView player={player} style={slothStyles.playerVideoView} contentFit='contain' allowsFullscreen /> : <View style={slothStyles.playerVideoView}/>}

      {isBuffering && <View style={slothStyles.playerLoadingContainer}><ActivityIndicator size="large" color="white" /></View>}
      
      {/* Invisible Gesture Overlay */}
      <GestureDetector gesture={singleTap}>
        <View style={{ ...StyleSheet.absoluteFillObject, flexDirection: 'row', zIndex: 1 }}>
            <GestureDetector gesture={doubleTapSeek(false)}><View style={{flex: 1}}/></GestureDetector>
            <GestureDetector gesture={doubleTapPlayPause}><View style={{flex: 1.5}}/></GestureDetector>
            <GestureDetector gesture={doubleTapSeek(true)}><View style={{flex: 1}}/></GestureDetector>
        </View>
      </GestureDetector>

      <Animated.View style={[slothStyles.seekIndicator, seekIndicatorStyle]}>
          <View style={slothStyles.seekIndicatorIcon}>
              <Ionicons name={seekIcon} size={36} color="white" />
          </View>
      </Animated.View>

      <Animated.View style={[slothStyles.controlsOverlay, controlsStyle, {paddingTop: isLandscape ? 10 : insets.top, paddingBottom: isLandscape ? 10 : insets.bottom + 5}]}>
          <LinearGradient colors={['rgba(0,0,0,0.6)', 'transparent']} style={{position:'absolute', top:0, left:0, right:0, height:100, zIndex: -1}} />
          <View style={[slothStyles.topControls, { paddingHorizontal: isLandscape ? insets.left : 5 }]}>
              <TouchableOpacity style={slothStyles.playerIconButton} onPress={() => navigation.goBack()}><Ionicons name="arrow-back" size={24} color="white"/></TouchableOpacity>
              <Text style={slothStyles.playerTitle} numberOfLines={1}>{item.title || item.name}</Text>
              <TouchableOpacity style={slothStyles.playerIconButton}><Ionicons name="settings-outline" size={24} color="white"/></TouchableOpacity>
              <TouchableOpacity style={slothStyles.playerIconButton}><Ionicons name="text" size={24} color="white"/></TouchableOpacity>
          </View>

          <View style={[slothStyles.bottomControls, { paddingHorizontal: isLandscape ? insets.left : 5 }]}>
              <View style={slothStyles.timecodeContainer}>
                  <Text style={slothStyles.timecodeText}>{formatTime(position)}</Text>
                  <Text style={slothStyles.timecodeText}>{formatTime(duration)}</Text>
              </View>
              <View onLayout={e => setSliderWidth(e.nativeEvent.layout.width)}>
                {sliderWidth > 0 && <CustomSlider player={player} status={status} width={sliderWidth} />}
              </View>
              <View style={{alignItems: 'flex-end'}}>
                <TouchableOpacity style={slothStyles.playerIconButton} onPress={toggleFullscreen}>
                    <Ionicons name={isLandscape ? "contract" : "expand"} size={24} color="white" />
                </TouchableOpacity>
              </View>
          </View>
          <LinearGradient colors={['transparent', 'rgba(0,0,0,0.6)']} style={{position:'absolute', bottom:0, left:0, right:0, height:120, zIndex: -1}}/>
      </Animated.View>
    </GestureHandlerRootView>
  );
};

export default PlayerScreen;