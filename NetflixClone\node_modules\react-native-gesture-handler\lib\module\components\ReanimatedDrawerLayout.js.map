{"version": 3, "sources": ["ReanimatedDrawerLayout.tsx"], "names": ["React", "forwardRef", "useCallback", "useEffect", "useImperativeHandle", "useMemo", "useState", "StyleSheet", "Keyboard", "StatusBar", "I18nManager", "Platform", "Animated", "Extrapolation", "interpolate", "runOnJS", "useAnimatedProps", "useAnimatedStyle", "useDerivedValue", "useSharedValue", "with<PERSON><PERSON><PERSON>", "GestureObjects", "Gesture", "GestureDetector", "MouseB<PERSON>on", "DRAG_TOSS", "DrawerPosition", "DrawerState", "DrawerType", "DrawerLockMode", "Drawer<PERSON>eyboardDismissMode", "defaultProps", "drawerWidth", "drawerPosition", "LEFT", "drawerType", "FRONT", "edgeWidth", "minSwipeDistance", "overlayColor", "drawerLockMode", "UNLOCKED", "enableTrackpadTwoFingerGesture", "activeCursor", "mouseButton", "statusBarAnimation", "setStatusBarHidden", "setHidden", "dismissKeyboard", "dismiss", "DrawerLayout", "props", "ref", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drawerState", "setDrawerState", "IDLE", "drawerOpened", "set<PERSON><PERSON>er<PERSON><PERSON>ed", "drawerBackgroundColor", "drawerContainerStyle", "contentContainerStyle", "hideStatusBar", "keyboardDismissMode", "userSelect", "enableContextMenu", "renderNavigationView", "onDrawerSlide", "onDrawerClose", "onDrawerOpen", "onDrawerStateChanged", "animationSpeed", "animationSpeedProp", "isFromLeft", "sideCorrection", "openValue", "value", "isDrawerOpen", "handleContainerLayout", "nativeEvent", "layout", "width", "emitStateChanged", "newState", "drawerWillShow", "drawerAnimatedProps", "accessibilityViewIsModal", "overlayAnimatedProps", "pointerEvents", "edgeHitSlop", "setEdgeHitSlop", "left", "right", "gestureOrientation", "animateDrawer", "toValue", "initialVelocity", "willShow", "SETTLING", "normalizedToValue", "CLAMP", "normalizedInitialVelocity", "overshootClamping", "velocity", "mass", "damping", "stiffness", "finished", "handleRelease", "event", "translationX", "dragX", "velocityX", "x", "touchX", "gestureStartX", "dragOffsetBasedOnStart", "startOffsetX", "projOffsetX", "shouldOpen", "openDrawer", "options", "closeDrawer", "overlayDismissGesture", "Tap", "maxDistance", "onEnd", "LOCKED_OPEN", "overlayAnimatedStyle", "opacity", "backgroundColor", "fillHitSlop", "panGesture", "Pan", "hitSlop", "minDistance", "activeOffsetX", "failOffsetY", "simultaneousWithExternalGesture", "enabled", "LOCKED_CLOSED", "onStart", "DRAGGING", "ON_DRAG", "onUpdate", "startedOutsideTranslation", "startedInsideTranslation", "adjustedTranslation", "Math", "max", "reverseContentDirection", "isRTL", "dynamicDrawerStyles", "containerStyles", "transform", "translateX", "drawerAnimatedStyle", "closedDrawerOffset", "isBack", "BACK", "isIdle", "flexDirection", "containerAnimatedProps", "importantForAccessibility", "OS", "undefined", "children", "styles", "main", "containerOnBack", "containerInFront", "overlay", "drawerContainer", "create", "absoluteFillObject", "zIndex", "flex", "overflow"], "mappings": "AAAA;AACA;AACA;AAEA,OAAOA,KAAP,IAEEC,UAFF,EAGEC,WAHF,EAIEC,SAJF,EAKEC,mBALF,EAMEC,OANF,EAOEC,QAPF,QAQO,OARP;AAUA,SACEC,UADF,EAEEC,QAFF,EAGEC,SAHF,EAIEC,WAJF,EASEC,QATF,QAUO,cAVP;AAYA,OAAOC,QAAP,IACEC,aADF,EAGEC,WAHF,EAIEC,OAJF,EAKEC,gBALF,EAMEC,gBANF,EAOEC,eAPF,EAQEC,cARF,EASEC,UATF,QAUO,yBAVP;AAYA,SAASC,cAAc,IAAIC,OAA3B,QAA0C,qCAA1C;AACA,SAASC,eAAT,QAAgC,sCAAhC;AACA,SAGEC,WAHF,QAMO,kCANP;AASA,MAAMC,SAAS,GAAG,IAAlB;AAEA,WAAYC,cAAZ;;WAAYA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;GAAAA,c,KAAAA,c;;AAKZ,WAAYC,WAAZ;;WAAYA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;GAAAA,W,KAAAA,W;;AAMZ,WAAYC,UAAZ;;WAAYA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;GAAAA,U,KAAAA,U;;AAMZ,WAAYC,cAAZ;;WAAYA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;GAAAA,c,KAAAA,c;;AAMZ,WAAYC,yBAAZ;;WAAYA,yB;AAAAA,EAAAA,yB,CAAAA,yB;AAAAA,EAAAA,yB,CAAAA,yB;GAAAA,yB,KAAAA,yB;;AAgLZ,MAAMC,YAAY,GAAG;AACnBC,EAAAA,WAAW,EAAE,GADM;AAEnBC,EAAAA,cAAc,EAAEP,cAAc,CAACQ,IAFZ;AAGnBC,EAAAA,UAAU,EAAEP,UAAU,CAACQ,KAHJ;AAInBC,EAAAA,SAAS,EAAE,EAJQ;AAKnBC,EAAAA,gBAAgB,EAAE,CALC;AAMnBC,EAAAA,YAAY,EAAE,oBANK;AAOnBC,EAAAA,cAAc,EAAEX,cAAc,CAACY,QAPZ;AAQnBC,EAAAA,8BAA8B,EAAE,KARb;AASnBC,EAAAA,YAAY,EAAE,MATK;AAUnBC,EAAAA,WAAW,EAAEpB,WAAW,CAACU,IAVN;AAWnBW,EAAAA,kBAAkB,EAAE;AAXD,CAArB,C,CAcA;;AACA,MAAMC,kBAAkB,GAAGrC,SAAS,CAACsC,SAArC;AACA,MAAMC,eAAe,GAAGxC,QAAQ,CAACyC,OAAjC;AAEA,MAAMC,YAAY,gBAAGjD,UAAU,CAC7B,SAASiD,YAAT,CAAsBC,KAAtB,EAAgDC,GAAhD,EAAqD;AACnD,QAAM,CAACC,cAAD,EAAiBC,iBAAjB,IAAsChD,QAAQ,CAAC,CAAD,CAApD;AACA,QAAM,CAACiD,WAAD,EAAcC,cAAd,IAAgClD,QAAQ,CAC5CqB,WAAW,CAAC8B,IADgC,CAA9C;AAGA,QAAM,CAACC,YAAD,EAAeC,eAAf,IAAkCrD,QAAQ,CAAC,KAAD,CAAhD;AAEA,QAAM;AACJ2B,IAAAA,cAAc,GAAGF,YAAY,CAACE,cAD1B;AAEJD,IAAAA,WAAW,GAAGD,YAAY,CAACC,WAFvB;AAGJG,IAAAA,UAAU,GAAGJ,YAAY,CAACI,UAHtB;AAIJyB,IAAAA,qBAJI;AAKJC,IAAAA,oBALI;AAMJC,IAAAA,qBANI;AAOJxB,IAAAA,gBAAgB,GAAGP,YAAY,CAACO,gBAP5B;AAQJD,IAAAA,SAAS,GAAGN,YAAY,CAACM,SARrB;AASJG,IAAAA,cAAc,GAAGT,YAAY,CAACS,cAT1B;AAUJD,IAAAA,YAAY,GAAGR,YAAY,CAACQ,YAVxB;AAWJG,IAAAA,8BAA8B,GAAGX,YAAY,CAACW,8BAX1C;AAYJC,IAAAA,YAAY,GAAGZ,YAAY,CAACY,YAZxB;AAaJC,IAAAA,WAAW,GAAGb,YAAY,CAACa,WAbvB;AAcJC,IAAAA,kBAAkB,GAAGd,YAAY,CAACc,kBAd9B;AAeJkB,IAAAA,aAfI;AAgBJC,IAAAA,mBAhBI;AAiBJC,IAAAA,UAjBI;AAkBJC,IAAAA,iBAlBI;AAmBJC,IAAAA,oBAnBI;AAoBJC,IAAAA,aApBI;AAqBJC,IAAAA,aArBI;AAsBJC,IAAAA,YAtBI;AAuBJC,IAAAA,oBAvBI;AAwBJC,IAAAA,cAAc,EAAEC;AAxBZ,MAyBFtB,KAzBJ;AA2BA,QAAMuB,UAAU,GAAGzC,cAAc,KAAKP,cAAc,CAACQ,IAArD;AAEA,QAAMyC,cAAc,GAAGD,UAAU,GAAG,CAAH,GAAO,CAAC,CAAzC,CApCmD,CAsCnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAME,SAAS,GAAGzD,cAAc,CAAS,CAAT,CAAhC;AAEAD,EAAAA,eAAe,CAAC,MAAM;AACpBkD,IAAAA,aAAa,IAAIrD,OAAO,CAACqD,aAAD,CAAP,CAAuBQ,SAAS,CAACC,KAAjC,CAAjB;AACD,GAFc,EAEZ,EAFY,CAAf;AAIA,QAAMC,YAAY,GAAG3D,cAAc,CAAC,KAAD,CAAnC;;AAEA,QAAM4D,qBAAqB,GAAG,CAAC;AAAEC,IAAAA;AAAF,GAAD,KAAwC;AACpE1B,IAAAA,iBAAiB,CAAC0B,WAAW,CAACC,MAAZ,CAAmBC,KAApB,CAAjB;AACD,GAFD;;AAIA,QAAMC,gBAAgB,GAAGjF,WAAW,CAClC,CAACkF,QAAD,EAAwBC,cAAxB,KAAoD;AAClD;;AADkD;;AAElDd,IAAAA,oBAAoB,iBAClBxD,OAAO,CAACwD,oBAAD,CADW,6CAClB,SAAgCa,QAAhC,EAA0CC,cAA1C,CADkB,CAApB;AAED,GALiC,EAMlC,CAACd,oBAAD,CANkC,CAApC;AASA,QAAMe,mBAAmB,GAAGtE,gBAAgB,CAAC,OAAO;AAClDuE,IAAAA,wBAAwB,EAAET,YAAY,CAACD;AADW,GAAP,CAAD,CAA5C;AAIA,QAAMW,oBAAoB,GAAGxE,gBAAgB,CAAC,OAAO;AACnDyE,IAAAA,aAAa,EAAEX,YAAY,CAACD,KAAb,GAAsB,MAAtB,GAA0C;AADN,GAAP,CAAD,CAA7C,CA1EmD,CA8EnD;AACA;;AACA,QAAM,CAACa,WAAD,EAAcC,cAAd,IAAgCrF,QAAQ,CAC5CoE,UAAU,GACN;AAAEkB,IAAAA,IAAI,EAAE,CAAR;AAAWV,IAAAA,KAAK,EAAE7C;AAAlB,GADM,GAEN;AAAEwD,IAAAA,KAAK,EAAE,CAAT;AAAYX,IAAAA,KAAK,EAAE7C;AAAnB,GAHwC,CAA9C,CAhFmD,CAsFnD;;AACA,QAAMyD,kBAAkB,GAAGzF,OAAO,CAChC,MAAMsE,cAAc,IAAIjB,YAAY,GAAG,CAAC,CAAJ,GAAQ,CAAxB,CADY,EAEhC,CAACiB,cAAD,EAAiBjB,YAAjB,CAFgC,CAAlC;AAKAvD,EAAAA,SAAS,CAAC,MAAM;AACdwF,IAAAA,cAAc,CACZjB,UAAU,GACN;AAAEkB,MAAAA,IAAI,EAAE,CAAR;AAAWV,MAAAA,KAAK,EAAE7C;AAAlB,KADM,GAEN;AAAEwD,MAAAA,KAAK,EAAE,CAAT;AAAYX,MAAAA,KAAK,EAAE7C;AAAnB,KAHQ,CAAd;AAKD,GANQ,EAMN,CAACqC,UAAD,EAAarC,SAAb,CANM,CAAT;AAQA,QAAM0D,aAAa,GAAG7F,WAAW,CAC/B,CAAC8F,OAAD,EAAkBC,eAAlB,EAA2CzB,cAA3C,KAAuE;AACrE;;AACA,UAAM0B,QAAQ,GAAGF,OAAO,KAAK,CAA7B;AACAlB,IAAAA,YAAY,CAACD,KAAb,GAAqBqB,QAArB;AAEAf,IAAAA,gBAAgB,CAACxD,WAAW,CAACwE,QAAb,EAAuBD,QAAvB,CAAhB;AACAnF,IAAAA,OAAO,CAACyC,cAAD,CAAP,CAAwB7B,WAAW,CAACwE,QAApC;;AAEA,QAAIpC,aAAJ,EAAmB;AACjBhD,MAAAA,OAAO,CAAC+B,kBAAD,CAAP,CAA4BoD,QAA5B,EAAsCrD,kBAAtC;AACD;;AAED,UAAMuD,iBAAiB,GAAGtF,WAAW,CACnCkF,OADmC,EAEnC,CAAC,CAAD,EAAIhE,WAAJ,CAFmC,EAGnC,CAAC,CAAD,EAAI,CAAJ,CAHmC,EAInCnB,aAAa,CAACwF,KAJqB,CAArC;AAOA,UAAMC,yBAAyB,GAAGxF,WAAW,CAC3CmF,eAD2C,EAE3C,CAAC,CAAD,EAAIjE,WAAJ,CAF2C,EAG3C,CAAC,CAAD,EAAI,CAAJ,CAH2C,EAI3CnB,aAAa,CAACwF,KAJ6B,CAA7C;AAOAzB,IAAAA,SAAS,CAACC,KAAV,GAAkBzD,UAAU,CAC1BgF,iBAD0B,EAE1B;AACEG,MAAAA,iBAAiB,EAAE,IADrB;AAEEC,MAAAA,QAAQ,EAAEF,yBAFZ;AAGEG,MAAAA,IAAI,EAAEjC,cAAc,GAChB,IAAIA,cADY,GAEhB,KAAKC,kBAAL,aAAKA,kBAAL,cAAKA,kBAAL,GAA2B,CAA3B,CALN;AAMEiC,MAAAA,OAAO,EAAE,EANX;AAOEC,MAAAA,SAAS,EAAE;AAPb,KAF0B,EAWzBC,QAAD,IAAc;AACZ,UAAIA,QAAJ,EAAc;AACZzB,QAAAA,gBAAgB,CAACxD,WAAW,CAAC8B,IAAb,EAAmByC,QAAnB,CAAhB;AACAnF,QAAAA,OAAO,CAAC4C,eAAD,CAAP,CAAyBuC,QAAzB;AACAnF,QAAAA,OAAO,CAACyC,cAAD,CAAP,CAAwB7B,WAAW,CAAC8B,IAApC;;AACA,YAAIyC,QAAJ,EAAc;AAAA;;AACZ5B,UAAAA,YAAY,kBAAIvD,OAAO,CAACuD,YAAD,CAAX,8CAAI,WAAJ,CAAZ;AACD,SAFD,MAEO;AAAA;;AACLD,UAAAA,aAAa,kBAAItD,OAAO,CAACsD,aAAD,CAAX,8CAAI,WAAJ,CAAb;AACD;AACF;AACF,KAtByB,CAA5B;AAwBD,GAnD8B,EAoD/B,CACEO,SADF,EAEEO,gBAFF,EAGEL,YAHF,EAIEf,aAJF,EAKEM,aALF,EAMEC,YANF,EAOEtC,WAPF,EAQEa,kBARF,CApD+B,CAAjC;AAgEA,QAAMgE,aAAa,GAAG3G,WAAW,CAC9B4G,KAAD,IAAmE;AACjE;;AACA,QAAI;AAAEC,MAAAA,YAAY,EAAEC,KAAhB;AAAuBC,MAAAA,SAAvB;AAAkCC,MAAAA,CAAC,EAAEC;AAArC,QAAgDL,KAApD;;AAEA,QAAI7E,cAAc,KAAKP,cAAc,CAACQ,IAAtC,EAA4C;AAC1C;AACA;AACA8E,MAAAA,KAAK,GAAG,CAACA,KAAT;AACAG,MAAAA,MAAM,GAAG9D,cAAc,GAAG8D,MAA1B;AACAF,MAAAA,SAAS,GAAG,CAACA,SAAb;AACD;;AAED,UAAMG,aAAa,GAAGD,MAAM,GAAGH,KAA/B;AACA,QAAIK,sBAAsB,GAAG,CAA7B;;AAEA,QAAIlF,UAAU,KAAKP,UAAU,CAACQ,KAA9B,EAAqC;AACnCiF,MAAAA,sBAAsB,GACpBD,aAAa,GAAGpF,WAAhB,GAA8BoF,aAAa,GAAGpF,WAA9C,GAA4D,CAD9D;AAED;;AAED,UAAMsF,YAAY,GAChBN,KAAK,GACLK,sBADA,IAECvC,YAAY,CAACD,KAAb,GAAqB7C,WAArB,GAAmC,CAFpC,CADF;AAKA,UAAMuF,WAAW,GAAGD,YAAY,GAAG7F,SAAS,GAAGwF,SAA/C;AAEA,UAAMO,UAAU,GAAGD,WAAW,GAAGvF,WAAW,GAAG,CAA/C;;AAEA,QAAIwF,UAAJ,EAAgB;AACdzB,MAAAA,aAAa,CAAC/D,WAAD,EAAciF,SAAd,CAAb;AACD,KAFD,MAEO;AACLlB,MAAAA,aAAa,CAAC,CAAD,EAAIkB,SAAJ,CAAb;AACD;AACF,GAnC8B,EAoC/B,CACElB,aADF,EAEE1C,cAFF,EAGEpB,cAHF,EAIEE,UAJF,EAKEH,WALF,EAME8C,YANF,CApC+B,CAAjC;AA8CA,QAAM2C,UAAU,GAAGvH,WAAW,CAC5B,CAACwH,OAA6B,GAAG,EAAjC,KAAwC;AACtC;;AADsC;;AAEtC3B,IAAAA,aAAa,CACX/D,WADW,2BAEX0F,OAAO,CAACzB,eAFG,yEAEgB,CAFhB,EAGXyB,OAAO,CAAClD,cAHG,CAAb;AAKD,GAR2B,EAS5B,CAACuB,aAAD,EAAgB/D,WAAhB,CAT4B,CAA9B;AAYA,QAAM2F,WAAW,GAAGzH,WAAW,CAC7B,CAACwH,OAA6B,GAAG,EAAjC,KAAwC;AACtC;;AADsC;;AAEtC3B,IAAAA,aAAa,CAAC,CAAD,4BAAI2B,OAAO,CAACzB,eAAZ,2EAA+B,CAA/B,EAAkCyB,OAAO,CAAClD,cAA1C,CAAb;AACD,GAJ4B,EAK7B,CAACuB,aAAD,CAL6B,CAA/B;AAQA,QAAM6B,qBAAqB,GAAGvH,OAAO,CACnC,MACEiB,OAAO,CAACuG,GAAR,GACGC,WADH,CACe,EADf,EAEGC,KAFH,CAES,MAAM;AACX,QACEjD,YAAY,CAACD,KAAb,IACArC,cAAc,KAAKX,cAAc,CAACmG,WAFpC,EAGE;AACAL,MAAAA,WAAW;AACZ;AACF,GATH,CAFiC,EAYnC,CAACA,WAAD,EAAc7C,YAAd,EAA4BtC,cAA5B,CAZmC,CAArC;AAeA,QAAMyF,oBAAoB,GAAGhH,gBAAgB,CAAC,OAAO;AACnDiH,IAAAA,OAAO,EAAEtD,SAAS,CAACC,KADgC;AAEnDsD,IAAAA,eAAe,EAAE5F;AAFkC,GAAP,CAAD,CAA7C;AAKA,QAAM6F,WAAW,GAAG/H,OAAO,CACzB,MAAOqE,UAAU,GAAG;AAAEkB,IAAAA,IAAI,EAAE5D;AAAR,GAAH,GAA2B;AAAE6D,IAAAA,KAAK,EAAE7D;AAAT,GADnB,EAEzB,CAACA,WAAD,EAAc0C,UAAd,CAFyB,CAA3B;AAKA,QAAM2D,UAAU,GAAGhI,OAAO,CAAC,MAAM;AAC/B,WAAOiB,OAAO,CAACgH,GAAR,GACJ3F,YADI,CACSA,YADT,EAEJC,WAFI,CAEQA,WAFR,EAGJ2F,OAHI,CAGI7E,YAAY,GAAG0E,WAAH,GAAiB1C,WAHjC,EAIJ8C,WAJI,CAIQ9E,YAAY,GAAG,GAAH,GAAS,CAJ7B,EAKJ+E,aALI,CAKU3C,kBAAkB,GAAGxD,gBAL/B,EAMJoG,WANI,CAMQ,CAAC,CAAC,EAAF,EAAM,EAAN,CANR,EAOJC,+BAPI,CAO4Bf,qBAP5B,EAQJlF,8BARI,CAQ2BA,8BAR3B,EASJkG,OATI,CAUHrF,WAAW,KAAK5B,WAAW,CAACwE,QAA5B,KACGzC,YAAY,GACTlB,cAAc,KAAKX,cAAc,CAACmG,WADzB,GAETxF,cAAc,KAAKX,cAAc,CAACgH,aAHxC,CAVG,EAeJC,OAfI,CAeI,MAAM;AACb3D,MAAAA,gBAAgB,CAACxD,WAAW,CAACoH,QAAb,EAAuB,KAAvB,CAAhB;AACAhI,MAAAA,OAAO,CAACyC,cAAD,CAAP,CAAwB7B,WAAW,CAACoH,QAApC;;AACA,UAAI/E,mBAAmB,KAAKlC,yBAAyB,CAACkH,OAAtD,EAA+D;AAC7DjI,QAAAA,OAAO,CAACiC,eAAD,CAAP;AACD;;AACD,UAAIe,aAAJ,EAAmB;AACjBhD,QAAAA,OAAO,CAAC+B,kBAAD,CAAP,CAA4B,IAA5B,EAAkCD,kBAAlC;AACD;AACF,KAxBI,EAyBJoG,QAzBI,CAyBMnC,KAAD,IAAW;AACnB,YAAMoC,yBAAyB,GAAGxE,UAAU,GACxC5D,WAAW,CACTgG,KAAK,CAACI,CADG,EAET,CAAC,CAAD,EAAIlF,WAAJ,EAAiBA,WAAW,GAAG,CAA/B,CAFS,EAGT,CAAC,CAAD,EAAIA,WAAJ,EAAiBA,WAAjB,CAHS,CAD6B,GAMxClB,WAAW,CACTgG,KAAK,CAACI,CAAN,GAAU7D,cADD,EAET,CAAC,CAACrB,WAAD,GAAe,CAAhB,EAAmB,CAACA,WAApB,EAAiC,CAAjC,CAFS,EAGT,CAACA,WAAD,EAAcA,WAAd,EAA2B,CAA3B,CAHS,CANf;AAYA,YAAMmH,wBAAwB,GAC5BxE,cAAc,IACbmC,KAAK,CAACC,YAAN,IACErD,YAAY,GAAG1B,WAAW,GAAG,CAAC8D,kBAAlB,GAAuC,CADrD,CADa,CADhB;AAKA,YAAMsD,mBAAmB,GAAGC,IAAI,CAACC,GAAL,CAC1B5F,YAAY,GAAGwF,yBAAH,GAA+B,CADjB,EAE1BC,wBAF0B,CAA5B;AAKAvE,MAAAA,SAAS,CAACC,KAAV,GAAkB/D,WAAW,CAC3BsI,mBAD2B,EAE3B,CAAC,CAACpH,WAAF,EAAe,CAAf,EAAkBA,WAAlB,CAF2B,EAG3B,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAH2B,EAI3BnB,aAAa,CAACwF,KAJa,CAA7B;AAMD,KAtDI,EAuDJ0B,KAvDI,CAuDElB,aAvDF,CAAP;AAwDD,GAzDyB,EAyDvB,CACDrE,cADC,EAEDoC,SAFC,EAGD5C,WAHC,EAIDmD,gBAJC,EAKDW,kBALC,EAMDe,aANC,EAODnB,WAPC,EAQD0C,WARC,EASD9F,gBATC,EAUDyB,aAVC,EAWDC,mBAXC,EAYD4D,qBAZC,EAaDlE,YAbC,EAcDgB,UAdC,EAeDrB,cAfC,EAgBDsB,cAhBC,EAiBDpB,WAjBC,EAkBDZ,YAlBC,EAmBDD,8BAnBC,EAoBDE,WApBC,EAqBDC,kBArBC,CAzDuB,CAA1B,CA/PmD,CAgVnD;;AACA,QAAM0G,uBAAuB,GAAG7I,WAAW,CAAC8I,KAAZ,GAC5B9E,UAD4B,GAE5B,CAACA,UAFL;AAIA,QAAM+E,mBAAmB,GAAG;AAC1BtB,IAAAA,eAAe,EAAEvE,qBADS;AAE1BsB,IAAAA,KAAK,EAAElD;AAFmB,GAA5B;AAKA,QAAM0H,eAAe,GAAGzI,gBAAgB,CAAC,MAAM;AAC7C,QAAIkB,UAAU,KAAKP,UAAU,CAACQ,KAA9B,EAAqC;AACnC,aAAO,EAAP;AACD;;AAED,WAAO;AACLuH,MAAAA,SAAS,EAAE,CACT;AACEC,QAAAA,UAAU,EAAE9I,WAAW,CACrB8D,SAAS,CAACC,KADW,EAErB,CAAC,CAAD,EAAI,CAAJ,CAFqB,EAGrB,CAAC,CAAD,EAAI7C,WAAW,GAAG2C,cAAlB,CAHqB,EAIrB9D,aAAa,CAACwF,KAJO;AADzB,OADS;AADN,KAAP;AAYD,GAjBuC,CAAxC;AAmBA,QAAMwD,mBAAmB,GAAG5I,gBAAgB,CAAC,MAAM;AACjD,UAAM6I,kBAAkB,GAAG9H,WAAW,GAAG,CAAC2C,cAA1C;AACA,UAAMoF,MAAM,GAAG5H,UAAU,KAAKP,UAAU,CAACoI,IAAzC;AACA,UAAMC,MAAM,GAAG1G,WAAW,KAAK5B,WAAW,CAAC8B,IAA3C;;AAEA,QAAIsG,MAAJ,EAAY;AACV,aAAO;AACLJ,QAAAA,SAAS,EAAE,CAAC;AAAEC,UAAAA,UAAU,EAAE;AAAd,SAAD,CADN;AAELM,QAAAA,aAAa,EAAEX,uBAAuB,GAAG,aAAH,GAAmB;AAFpD,OAAP;AAID;;AAED,QAAIK,UAAU,GAAG,CAAjB;;AAEA,QAAIK,MAAJ,EAAY;AACVL,MAAAA,UAAU,GAAGlG,YAAY,GAAG,CAAH,GAAOoG,kBAAhC;AACD,KAFD,MAEO;AACLF,MAAAA,UAAU,GAAG9I,WAAW,CACtB8D,SAAS,CAACC,KADY,EAEtB,CAAC,CAAD,EAAI,CAAJ,CAFsB,EAGtB,CAACiF,kBAAD,EAAqB,CAArB,CAHsB,EAItBjJ,aAAa,CAACwF,KAJQ,CAAxB;AAMD;;AAED,WAAO;AACLsD,MAAAA,SAAS,EAAE,CAAC;AAAEC,QAAAA;AAAF,OAAD,CADN;AAELM,MAAAA,aAAa,EAAEX,uBAAuB,GAAG,aAAH,GAAmB;AAFpD,KAAP;AAID,GA7B2C,CAA5C;AA+BA,QAAMY,sBAAsB,GAAGnJ,gBAAgB,CAAC,OAAO;AACrDoJ,IAAAA,yBAAyB,EACvBzJ,QAAQ,CAAC0J,EAAT,KAAgB,SAAhB,GACIvF,YAAY,CAACD,KAAb,GACG,qBADH,GAEG,KAHP,GAIIyF;AAN+C,GAAP,CAAD,CAA/C;AASA,QAAMC,QAAQ,GACZ,OAAOpH,KAAK,CAACoH,QAAb,KAA0B,UAA1B,GACIpH,KAAK,CAACoH,QAAN,CAAe3F,SAAf,CADJ,CAC8B;AAD9B,IAEIzB,KAAK,CAACoH,QAHZ;AAKAnK,EAAAA,mBAAmB,CACjBgD,GADiB,EAEjB,OAAO;AACLqE,IAAAA,UADK;AAELE,IAAAA;AAFK,GAAP,CAFiB,EAMjB,CAACF,UAAD,EAAaE,WAAb,CANiB,CAAnB;AASA,sBACE,oBAAC,eAAD;AACE,IAAA,OAAO,EAAEU,UADX;AAEE,IAAA,UAAU,EAAEpE,UAFd;AAGE,IAAA,iBAAiB,EAAEC;AAHrB,kBAIE,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAEsG,MAAM,CAACC,IAA7B;AAAmC,IAAA,QAAQ,EAAE1F;AAA7C,kBACE,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAE6C;AAA1B,kBACE,oBAAC,QAAD,CAAU,IAAV;AACE,IAAA,KAAK,EAAE,CACLzF,UAAU,KAAKP,UAAU,CAACQ,KAA1B,GACIoI,MAAM,CAACE,eADX,GAEIF,MAAM,CAACG,gBAHN,EAILjB,eAJK,EAKL5F,qBALK,CADT;AAQE,IAAA,aAAa,EAAEqG;AARjB,KASGI,QATH,eAUE,oBAAC,QAAD,CAAU,IAAV;AACE,IAAA,aAAa,EAAE/E,oBADjB;AAEE,IAAA,KAAK,EAAE,CAACgF,MAAM,CAACI,OAAR,EAAiB3C,oBAAjB;AAFT,IAVF,CADF,CADF,eAkBE,oBAAC,QAAD,CAAU,IAAV;AACE,IAAA,aAAa,EAAC,UADhB;AAEE,IAAA,aAAa,EAAE3C,mBAFjB;AAGE,IAAA,KAAK,EAAE,CACLkF,MAAM,CAACK,eADF,EAELhB,mBAFK,EAGLhG,oBAHK;AAHT,kBAQE,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE4F;AAAtB,KACGtF,oBAAoB,CAACS,SAAD,CADvB,CARF,CAlBF,CAJF,CADF;AAsCD,CA1c4B,CAA/B;AA6cA,eAAe1B,YAAf;AAEA,MAAMsH,MAAM,GAAGjK,UAAU,CAACuK,MAAX,CAAkB;AAC/BD,EAAAA,eAAe,EAAE,EACf,GAAGtK,UAAU,CAACwK,kBADC;AAEfC,IAAAA,MAAM,EAAE,IAFO;AAGfd,IAAAA,aAAa,EAAE;AAHA,GADc;AAM/BS,EAAAA,gBAAgB,EAAE,EAChB,GAAGpK,UAAU,CAACwK,kBADE;AAEhBC,IAAAA,MAAM,EAAE;AAFQ,GANa;AAU/BN,EAAAA,eAAe,EAAE,EACf,GAAGnK,UAAU,CAACwK;AADC,GAVc;AAa/BN,EAAAA,IAAI,EAAE;AACJQ,IAAAA,IAAI,EAAE,CADF;AAEJD,IAAAA,MAAM,EAAE,CAFJ;AAGJE,IAAAA,QAAQ,EAAE;AAHN,GAbyB;AAkB/BN,EAAAA,OAAO,EAAE,EACP,GAAGrK,UAAU,CAACwK,kBADP;AAEPC,IAAAA,MAAM,EAAE;AAFD;AAlBsB,CAAlB,CAAf", "sourcesContent": ["// This component is based on RN's DrawerLayoutAndroid API\n// It's cross-compatible with all platforms despite\n// `DrawerLayoutAndroid` only being available on android\n\nimport React, {\n  ReactNode,\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperativeHandle,\n  useMemo,\n  useState,\n} from 'react';\n\nimport {\n  StyleSheet,\n  Keyboard,\n  StatusBar,\n  I18nManager,\n  StatusBarAnimation,\n  StyleProp,\n  ViewStyle,\n  LayoutChangeEvent,\n  Platform,\n} from 'react-native';\n\nimport Animated, {\n  Extrapolation,\n  SharedValue,\n  interpolate,\n  runOnJS,\n  useAnimatedProps,\n  useAnimatedStyle,\n  useDerivedValue,\n  useSharedValue,\n  withSpring,\n} from 'react-native-reanimated';\n\nimport { GestureObjects as Gesture } from '../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../handlers/gestures/GestureDetector';\nimport {\n  UserSelect,\n  ActiveCursor,\n  MouseButton,\n  HitSlop,\n  GestureStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport { PanGestureHandlerEventPayload } from '../handlers/GestureHandlerEventPayload';\n\nconst DRAG_TOSS = 0.05;\n\nexport enum DrawerPosition {\n  LEFT,\n  RIGHT,\n}\n\nexport enum DrawerState {\n  IDLE,\n  DRAGGING,\n  SETTLING,\n}\n\nexport enum DrawerType {\n  FRONT,\n  BACK,\n  SLIDE,\n}\n\nexport enum DrawerLockMode {\n  UNLOCKED,\n  LOCKED_CLOSED,\n  LOCKED_OPEN,\n}\n\nexport enum DrawerKeyboardDismissMode {\n  NONE,\n  ON_DRAG,\n}\n\nexport interface DrawerLayoutProps {\n  /**\n   * This attribute is present in the native android implementation already and is one\n   * of the required params. The gesture handler version of DrawerLayout makes it\n   * possible for the function passed as `renderNavigationView` to take an\n   * Animated value as a parameter that indicates the progress of drawer\n   * opening/closing animation (progress value is 0 when closed and 1 when\n   * opened). This can be used by the drawer component to animated its children\n   * while the drawer is opening or closing.\n   */\n  renderNavigationView: (\n    progressAnimatedValue: SharedValue<number>\n  ) => ReactNode;\n\n  /**\n   * Determines the side from which the drawer will open.\n   */\n  drawerPosition?: DrawerPosition;\n\n  /**\n   * Width of the drawer.\n   */\n  drawerWidth?: number;\n\n  /**\n   * Background color of the drawer.\n   */\n  drawerBackgroundColor?: string;\n\n  /**\n   * Specifies the lock mode of the drawer.\n   * Programatic opening/closing isn't affected by the lock mode. Defaults to `UNLOCKED`.\n   * - `UNLOCKED` - the drawer will respond to gestures.\n   * - `LOCKED_CLOSED` - the drawer will move freely until it settles in a closed position, then the gestures will be disabled.\n   * - `LOCKED_OPEN` - the drawer will move freely until it settles in an opened position, then the gestures will be disabled.\n   */\n  drawerLockMode?: DrawerLockMode;\n\n  /**\n   * Determines if system keyboard should be closed upon dragging the drawer.\n   */\n  keyboardDismissMode?: DrawerKeyboardDismissMode;\n\n  /**\n   * Called when the drawer is closed.\n   */\n  onDrawerClose?: () => void;\n\n  /**\n   * Called when the drawer is opened.\n   */\n  onDrawerOpen?: () => void;\n\n  /**\n   * Called when the status of the drawer changes.\n   */\n  onDrawerStateChanged?: (\n    newState: DrawerState,\n    drawerWillShow: boolean\n  ) => void;\n\n  /**\n   * Type of animation that will play when opening the drawer.\n   */\n  drawerType?: DrawerType;\n\n  /**\n   * Speed of animation that will play when letting go, or dismissing the drawer.\n   * This will also be the default animation speed for programatic controlls.\n   */\n  animationSpeed?: number;\n\n  /**\n   * Defines how far from the edge of the content view the gesture should\n   * activate.\n   */\n  edgeWidth?: number;\n\n  /**\n   * Minimal distance to swipe before the drawer starts moving.\n   */\n  minSwipeDistance?: number;\n\n  /**\n   * When set to true Drawer component will use\n   * {@link https://reactnative.dev/docs/statusbar StatusBar} API to hide the OS\n   * status bar whenever the drawer is pulled or when its in an \"open\" state.\n   */\n  hideStatusBar?: boolean;\n\n  /**\n   * @default 'slide'\n   *\n   * Can be used when hideStatusBar is set to true and will select the animation\n   * used for hiding/showing the status bar. See\n   * {@link https://reactnative.dev/docs/statusbar StatusBar} documentation for\n   * more details\n   */\n  statusBarAnimation?: StatusBarAnimation;\n\n  /**\n   * @default 'rgba(0, 0, 0, 0.7)'\n   *\n   * Color of the background overlay.\n   * Animated from `0%` to `100%` as the drawer opens.\n   */\n  overlayColor?: string;\n\n  /**\n   * Style wrapping the content.\n   */\n  contentContainerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Style wrapping the drawer.\n   */\n  drawerContainerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * `enableTrackpadTwoFingerGesture` swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  onDrawerSlide?: (position: number) => void;\n\n  // Implicit `children` prop has been removed in @types/react^18.0.\n  /**\n   * Elements that will be rendered inside the content view.\n   */\n  children?: ReactNode | ((openValue?: SharedValue<number>) => ReactNode);\n\n  /**\n   * @default 'none'\n   * Sets whether the text inside both the drawer and the context window can be selected.\n   * Values: 'none' | 'text' | 'auto'\n   */\n  userSelect?: UserSelect;\n\n  /**\n   * @default 'auto'\n   * Sets the displayed cursor pictogram when the drawer is being dragged.\n   * Values: see CSS cursor values\n   */\n  activeCursor?: ActiveCursor;\n\n  /**\n   * @default 'MouseButton.LEFT'\n   * Allows to choose which mouse button should underlying pan handler react to.\n   */\n  mouseButton?: MouseButton;\n\n  /**\n   * @default 'false if MouseButton.RIGHT is specified'\n   * Allows to enable/disable context menu.\n   */\n  enableContextMenu?: boolean;\n}\n\nexport type DrawerMovementOption = {\n  initialVelocity?: number;\n  animationSpeed?: number;\n};\n\nexport interface DrawerLayoutMethods {\n  openDrawer: (options?: DrawerMovementOption) => void;\n  closeDrawer: (options?: DrawerMovementOption) => void;\n}\n\nconst defaultProps = {\n  drawerWidth: 200,\n  drawerPosition: DrawerPosition.LEFT,\n  drawerType: DrawerType.FRONT,\n  edgeWidth: 20,\n  minSwipeDistance: 3,\n  overlayColor: 'rgba(0, 0, 0, 0.7)',\n  drawerLockMode: DrawerLockMode.UNLOCKED,\n  enableTrackpadTwoFingerGesture: false,\n  activeCursor: 'auto' as ActiveCursor,\n  mouseButton: MouseButton.LEFT,\n  statusBarAnimation: 'slide' as StatusBarAnimation,\n};\n\n// StatusBar.setHidden and Keyboard.dismiss cannot be directly referenced in worklets.\nconst setStatusBarHidden = StatusBar.setHidden;\nconst dismissKeyboard = Keyboard.dismiss;\n\nconst DrawerLayout = forwardRef<DrawerLayoutMethods, DrawerLayoutProps>(\n  function DrawerLayout(props: DrawerLayoutProps, ref) {\n    const [containerWidth, setContainerWidth] = useState(0);\n    const [drawerState, setDrawerState] = useState<DrawerState>(\n      DrawerState.IDLE\n    );\n    const [drawerOpened, setDrawerOpened] = useState(false);\n\n    const {\n      drawerPosition = defaultProps.drawerPosition,\n      drawerWidth = defaultProps.drawerWidth,\n      drawerType = defaultProps.drawerType,\n      drawerBackgroundColor,\n      drawerContainerStyle,\n      contentContainerStyle,\n      minSwipeDistance = defaultProps.minSwipeDistance,\n      edgeWidth = defaultProps.edgeWidth,\n      drawerLockMode = defaultProps.drawerLockMode,\n      overlayColor = defaultProps.overlayColor,\n      enableTrackpadTwoFingerGesture = defaultProps.enableTrackpadTwoFingerGesture,\n      activeCursor = defaultProps.activeCursor,\n      mouseButton = defaultProps.mouseButton,\n      statusBarAnimation = defaultProps.statusBarAnimation,\n      hideStatusBar,\n      keyboardDismissMode,\n      userSelect,\n      enableContextMenu,\n      renderNavigationView,\n      onDrawerSlide,\n      onDrawerClose,\n      onDrawerOpen,\n      onDrawerStateChanged,\n      animationSpeed: animationSpeedProp,\n    } = props;\n\n    const isFromLeft = drawerPosition === DrawerPosition.LEFT;\n\n    const sideCorrection = isFromLeft ? 1 : -1;\n\n    // While closing the drawer when user starts gesture in the greyed out part of the window,\n    // we want the drawer to follow only once the finger reaches the edge of the drawer.\n    // See the diagram for reference. * = starting finger position, < = current finger position\n    // 1) +---------------+ 2) +---------------+ 3) +---------------+ 4) +---------------+\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|..<*..|    |XXXXXXXX|.<-*..|    |XXXXXXXX|<--*..|    |XXXXX|<-----*..|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    +---------------+    +---------------+    +---------------+    +---------------+\n\n    const openValue = useSharedValue<number>(0);\n\n    useDerivedValue(() => {\n      onDrawerSlide && runOnJS(onDrawerSlide)(openValue.value);\n    }, []);\n\n    const isDrawerOpen = useSharedValue(false);\n\n    const handleContainerLayout = ({ nativeEvent }: LayoutChangeEvent) => {\n      setContainerWidth(nativeEvent.layout.width);\n    };\n\n    const emitStateChanged = useCallback(\n      (newState: DrawerState, drawerWillShow: boolean) => {\n        'worklet';\n        onDrawerStateChanged &&\n          runOnJS(onDrawerStateChanged)?.(newState, drawerWillShow);\n      },\n      [onDrawerStateChanged]\n    );\n\n    const drawerAnimatedProps = useAnimatedProps(() => ({\n      accessibilityViewIsModal: isDrawerOpen.value,\n    }));\n\n    const overlayAnimatedProps = useAnimatedProps(() => ({\n      pointerEvents: isDrawerOpen.value ? ('auto' as const) : ('none' as const),\n    }));\n\n    // While the drawer is hidden, it's hitSlop overflows onto the main view by edgeWidth\n    // This way it can be swiped open even when it's hidden\n    const [edgeHitSlop, setEdgeHitSlop] = useState<HitSlop>(\n      isFromLeft\n        ? { left: 0, width: edgeWidth }\n        : { right: 0, width: edgeWidth }\n    );\n\n    // gestureOrientation is 1 if the gesture is expected to move from left to right and -1 otherwise\n    const gestureOrientation = useMemo(\n      () => sideCorrection * (drawerOpened ? -1 : 1),\n      [sideCorrection, drawerOpened]\n    );\n\n    useEffect(() => {\n      setEdgeHitSlop(\n        isFromLeft\n          ? { left: 0, width: edgeWidth }\n          : { right: 0, width: edgeWidth }\n      );\n    }, [isFromLeft, edgeWidth]);\n\n    const animateDrawer = useCallback(\n      (toValue: number, initialVelocity: number, animationSpeed?: number) => {\n        'worklet';\n        const willShow = toValue !== 0;\n        isDrawerOpen.value = willShow;\n\n        emitStateChanged(DrawerState.SETTLING, willShow);\n        runOnJS(setDrawerState)(DrawerState.SETTLING);\n\n        if (hideStatusBar) {\n          runOnJS(setStatusBarHidden)(willShow, statusBarAnimation);\n        }\n\n        const normalizedToValue = interpolate(\n          toValue,\n          [0, drawerWidth],\n          [0, 1],\n          Extrapolation.CLAMP\n        );\n\n        const normalizedInitialVelocity = interpolate(\n          initialVelocity,\n          [0, drawerWidth],\n          [0, 1],\n          Extrapolation.CLAMP\n        );\n\n        openValue.value = withSpring(\n          normalizedToValue,\n          {\n            overshootClamping: true,\n            velocity: normalizedInitialVelocity,\n            mass: animationSpeed\n              ? 1 / animationSpeed\n              : 1 / (animationSpeedProp ?? 1),\n            damping: 40,\n            stiffness: 500,\n          },\n          (finished) => {\n            if (finished) {\n              emitStateChanged(DrawerState.IDLE, willShow);\n              runOnJS(setDrawerOpened)(willShow);\n              runOnJS(setDrawerState)(DrawerState.IDLE);\n              if (willShow) {\n                onDrawerOpen && runOnJS(onDrawerOpen)?.();\n              } else {\n                onDrawerClose && runOnJS(onDrawerClose)?.();\n              }\n            }\n          }\n        );\n      },\n      [\n        openValue,\n        emitStateChanged,\n        isDrawerOpen,\n        hideStatusBar,\n        onDrawerClose,\n        onDrawerOpen,\n        drawerWidth,\n        statusBarAnimation,\n      ]\n    );\n\n    const handleRelease = useCallback(\n      (event: GestureStateChangeEvent<PanGestureHandlerEventPayload>) => {\n        'worklet';\n        let { translationX: dragX, velocityX, x: touchX } = event;\n\n        if (drawerPosition !== DrawerPosition.LEFT) {\n          // See description in _updateAnimatedEvent about why events are flipped\n          // for right-side drawer\n          dragX = -dragX;\n          touchX = containerWidth - touchX;\n          velocityX = -velocityX;\n        }\n\n        const gestureStartX = touchX - dragX;\n        let dragOffsetBasedOnStart = 0;\n\n        if (drawerType === DrawerType.FRONT) {\n          dragOffsetBasedOnStart =\n            gestureStartX > drawerWidth ? gestureStartX - drawerWidth : 0;\n        }\n\n        const startOffsetX =\n          dragX +\n          dragOffsetBasedOnStart +\n          (isDrawerOpen.value ? drawerWidth : 0);\n\n        const projOffsetX = startOffsetX + DRAG_TOSS * velocityX;\n\n        const shouldOpen = projOffsetX > drawerWidth / 2;\n\n        if (shouldOpen) {\n          animateDrawer(drawerWidth, velocityX);\n        } else {\n          animateDrawer(0, velocityX);\n        }\n      },\n      [\n        animateDrawer,\n        containerWidth,\n        drawerPosition,\n        drawerType,\n        drawerWidth,\n        isDrawerOpen,\n      ]\n    );\n\n    const openDrawer = useCallback(\n      (options: DrawerMovementOption = {}) => {\n        'worklet';\n        animateDrawer(\n          drawerWidth,\n          options.initialVelocity ?? 0,\n          options.animationSpeed\n        );\n      },\n      [animateDrawer, drawerWidth]\n    );\n\n    const closeDrawer = useCallback(\n      (options: DrawerMovementOption = {}) => {\n        'worklet';\n        animateDrawer(0, options.initialVelocity ?? 0, options.animationSpeed);\n      },\n      [animateDrawer]\n    );\n\n    const overlayDismissGesture = useMemo(\n      () =>\n        Gesture.Tap()\n          .maxDistance(25)\n          .onEnd(() => {\n            if (\n              isDrawerOpen.value &&\n              drawerLockMode !== DrawerLockMode.LOCKED_OPEN\n            ) {\n              closeDrawer();\n            }\n          }),\n      [closeDrawer, isDrawerOpen, drawerLockMode]\n    );\n\n    const overlayAnimatedStyle = useAnimatedStyle(() => ({\n      opacity: openValue.value,\n      backgroundColor: overlayColor,\n    }));\n\n    const fillHitSlop = useMemo(\n      () => (isFromLeft ? { left: drawerWidth } : { right: drawerWidth }),\n      [drawerWidth, isFromLeft]\n    );\n\n    const panGesture = useMemo(() => {\n      return Gesture.Pan()\n        .activeCursor(activeCursor)\n        .mouseButton(mouseButton)\n        .hitSlop(drawerOpened ? fillHitSlop : edgeHitSlop)\n        .minDistance(drawerOpened ? 100 : 0)\n        .activeOffsetX(gestureOrientation * minSwipeDistance)\n        .failOffsetY([-15, 15])\n        .simultaneousWithExternalGesture(overlayDismissGesture)\n        .enableTrackpadTwoFingerGesture(enableTrackpadTwoFingerGesture)\n        .enabled(\n          drawerState !== DrawerState.SETTLING &&\n            (drawerOpened\n              ? drawerLockMode !== DrawerLockMode.LOCKED_OPEN\n              : drawerLockMode !== DrawerLockMode.LOCKED_CLOSED)\n        )\n        .onStart(() => {\n          emitStateChanged(DrawerState.DRAGGING, false);\n          runOnJS(setDrawerState)(DrawerState.DRAGGING);\n          if (keyboardDismissMode === DrawerKeyboardDismissMode.ON_DRAG) {\n            runOnJS(dismissKeyboard)();\n          }\n          if (hideStatusBar) {\n            runOnJS(setStatusBarHidden)(true, statusBarAnimation);\n          }\n        })\n        .onUpdate((event) => {\n          const startedOutsideTranslation = isFromLeft\n            ? interpolate(\n                event.x,\n                [0, drawerWidth, drawerWidth + 1],\n                [0, drawerWidth, drawerWidth]\n              )\n            : interpolate(\n                event.x - containerWidth,\n                [-drawerWidth - 1, -drawerWidth, 0],\n                [drawerWidth, drawerWidth, 0]\n              );\n\n          const startedInsideTranslation =\n            sideCorrection *\n            (event.translationX +\n              (drawerOpened ? drawerWidth * -gestureOrientation : 0));\n\n          const adjustedTranslation = Math.max(\n            drawerOpened ? startedOutsideTranslation : 0,\n            startedInsideTranslation\n          );\n\n          openValue.value = interpolate(\n            adjustedTranslation,\n            [-drawerWidth, 0, drawerWidth],\n            [1, 0, 1],\n            Extrapolation.CLAMP\n          );\n        })\n        .onEnd(handleRelease);\n    }, [\n      drawerLockMode,\n      openValue,\n      drawerWidth,\n      emitStateChanged,\n      gestureOrientation,\n      handleRelease,\n      edgeHitSlop,\n      fillHitSlop,\n      minSwipeDistance,\n      hideStatusBar,\n      keyboardDismissMode,\n      overlayDismissGesture,\n      drawerOpened,\n      isFromLeft,\n      containerWidth,\n      sideCorrection,\n      drawerState,\n      activeCursor,\n      enableTrackpadTwoFingerGesture,\n      mouseButton,\n      statusBarAnimation,\n    ]);\n\n    // When using RTL, row and row-reverse flex directions are flipped.\n    const reverseContentDirection = I18nManager.isRTL\n      ? isFromLeft\n      : !isFromLeft;\n\n    const dynamicDrawerStyles = {\n      backgroundColor: drawerBackgroundColor,\n      width: drawerWidth,\n    };\n\n    const containerStyles = useAnimatedStyle(() => {\n      if (drawerType === DrawerType.FRONT) {\n        return {};\n      }\n\n      return {\n        transform: [\n          {\n            translateX: interpolate(\n              openValue.value,\n              [0, 1],\n              [0, drawerWidth * sideCorrection],\n              Extrapolation.CLAMP\n            ),\n          },\n        ],\n      };\n    });\n\n    const drawerAnimatedStyle = useAnimatedStyle(() => {\n      const closedDrawerOffset = drawerWidth * -sideCorrection;\n      const isBack = drawerType === DrawerType.BACK;\n      const isIdle = drawerState === DrawerState.IDLE;\n\n      if (isBack) {\n        return {\n          transform: [{ translateX: 0 }],\n          flexDirection: reverseContentDirection ? 'row-reverse' : 'row',\n        };\n      }\n\n      let translateX = 0;\n\n      if (isIdle) {\n        translateX = drawerOpened ? 0 : closedDrawerOffset;\n      } else {\n        translateX = interpolate(\n          openValue.value,\n          [0, 1],\n          [closedDrawerOffset, 0],\n          Extrapolation.CLAMP\n        );\n      }\n\n      return {\n        transform: [{ translateX }],\n        flexDirection: reverseContentDirection ? 'row-reverse' : 'row',\n      };\n    });\n\n    const containerAnimatedProps = useAnimatedProps(() => ({\n      importantForAccessibility:\n        Platform.OS === 'android'\n          ? isDrawerOpen.value\n            ? ('no-hide-descendants' as const)\n            : ('yes' as const)\n          : undefined,\n    }));\n\n    const children =\n      typeof props.children === 'function'\n        ? props.children(openValue) // renderer function\n        : props.children;\n\n    useImperativeHandle(\n      ref,\n      () => ({\n        openDrawer,\n        closeDrawer,\n      }),\n      [openDrawer, closeDrawer]\n    );\n\n    return (\n      <GestureDetector\n        gesture={panGesture}\n        userSelect={userSelect}\n        enableContextMenu={enableContextMenu}>\n        <Animated.View style={styles.main} onLayout={handleContainerLayout}>\n          <GestureDetector gesture={overlayDismissGesture}>\n            <Animated.View\n              style={[\n                drawerType === DrawerType.FRONT\n                  ? styles.containerOnBack\n                  : styles.containerInFront,\n                containerStyles,\n                contentContainerStyle,\n              ]}\n              animatedProps={containerAnimatedProps}>\n              {children}\n              <Animated.View\n                animatedProps={overlayAnimatedProps}\n                style={[styles.overlay, overlayAnimatedStyle]}\n              />\n            </Animated.View>\n          </GestureDetector>\n          <Animated.View\n            pointerEvents=\"box-none\"\n            animatedProps={drawerAnimatedProps}\n            style={[\n              styles.drawerContainer,\n              drawerAnimatedStyle,\n              drawerContainerStyle,\n            ]}>\n            <Animated.View style={dynamicDrawerStyles}>\n              {renderNavigationView(openValue)}\n            </Animated.View>\n          </Animated.View>\n        </Animated.View>\n      </GestureDetector>\n    );\n  }\n);\n\nexport default DrawerLayout;\n\nconst styles = StyleSheet.create({\n  drawerContainer: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1001,\n    flexDirection: 'row',\n  },\n  containerInFront: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1002,\n  },\n  containerOnBack: {\n    ...StyleSheet.absoluteFillObject,\n  },\n  main: {\n    flex: 1,\n    zIndex: 0,\n    overflow: 'hidden',\n  },\n  overlay: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1000,\n  },\n});\n"]}