{"version": 3, "sources": ["PinchGestureHandler.ts"], "names": ["State", "DEFAULT_TOUCH_SLOP", "Gesture<PERSON>andler", "ScaleGestureDetector", "PinchGestureHandler", "onScaleBegin", "detector", "startingSpan", "currentSpan", "onScale", "prevScaleFactor", "scale", "calculateScaleFactor", "tracker", "trackedPointersCount", "delta", "<PERSON><PERSON><PERSON><PERSON>", "velocity", "Math", "abs", "spanSlop", "state", "BEGAN", "activate", "onScaleEnd", "_detector", "scaleDetectorListener", "init", "ref", "propsRef", "shouldCancelWhenOutside", "transformNativeEvent", "focalX", "scaleGestureDetector", "focusX", "focalY", "focusY", "onPointerDown", "event", "addToTracker", "tryToSendTouchEvent", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "end", "fail", "onPointerRemove", "onPointerMove", "track", "onPointerOutOfBounds", "UNDETERMINED", "resetProgress", "begin", "force", "onReset"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAASC,kBAAT,QAAmC,cAAnC;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AACA,OAAOC,oBAAP,MAEO,mCAFP;AAIA,eAAe,MAAMC,mBAAN,SAAkCF,cAAlC,CAAiD;AAAA;AAAA;;AAAA,mCAC9C,CAD8C;;AAAA,sCAE3C,CAF2C;;AAAA,0CAIvC,CAJuC;;AAAA,sCAK3CD,kBAL2C;;AAAA,mDAOR;AACpDI,MAAAA,YAAY,EAAGC,QAAD,IAA6C;AACzD,aAAKC,YAAL,GAAoBD,QAAQ,CAACE,WAA7B;AACA,eAAO,IAAP;AACD,OAJmD;AAKpDC,MAAAA,OAAO,EAAGH,QAAD,IAA6C;AACpD,cAAMI,eAAuB,GAAG,KAAKC,KAArC;AACA,aAAKA,KAAL,IAAcL,QAAQ,CAACM,oBAAT,CACZ,KAAKC,OAAL,CAAaC,oBADD,CAAd;AAIA,cAAMC,KAAK,GAAGT,QAAQ,CAACU,SAAvB;;AACA,YAAID,KAAK,GAAG,CAAZ,EAAe;AACb,eAAKE,QAAL,GAAgB,CAAC,KAAKN,KAAL,GAAaD,eAAd,IAAiCK,KAAjD;AACD;;AAED,YACEG,IAAI,CAACC,GAAL,CAAS,KAAKZ,YAAL,GAAoBD,QAAQ,CAACE,WAAtC,KAAsD,KAAKY,QAA3D,IACA,KAAKC,KAAL,KAAerB,KAAK,CAACsB,KAFvB,EAGE;AACA,eAAKC,QAAL;AACD;;AACD,eAAO,IAAP;AACD,OAvBmD;AAwBpDC,MAAAA,UAAU,EACRC,SADU,IAGD,CAAE;AA3BuC,KAPQ;;AAAA,kDAqCT,IAAItB,oBAAJ,CACnD,KAAKuB,qBAD8C,CArCS;AAAA;;AAyCvDC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAkD;AAC3D,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AAEA,SAAKC,uBAAL,GAA+B,KAA/B;AACD;;AAESC,EAAAA,oBAAoB,GAAG;AAC/B,WAAO;AACLC,MAAAA,MAAM,EAAE,KAAKC,oBAAL,CAA0BC,MAD7B;AAELC,MAAAA,MAAM,EAAE,KAAKF,oBAAL,CAA0BG,MAF7B;AAGLnB,MAAAA,QAAQ,EAAE,KAAKA,QAHV;AAILN,MAAAA,KAAK,EAAE,KAAKA;AAJP,KAAP;AAMD;;AAES0B,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKzB,OAAL,CAAa0B,YAAb,CAA0BD,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AAEA,SAAKE,mBAAL,CAAyBF,KAAzB;AACD;;AAESG,EAAAA,YAAY,CAACH,KAAD,EAA4B;AAChD,SAAKzB,OAAL,CAAa0B,YAAb,CAA0BD,KAA1B;AACA,UAAMG,YAAN,CAAmBH,KAAnB;AACA,SAAKI,QAAL;AACA,SAAKT,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAKzB,OAAnD;AACD;;AAES+B,EAAAA,WAAW,CAACN,KAAD,EAA4B;AAC/C,UAAMM,WAAN,CAAkBN,KAAlB;AACA,SAAKzB,OAAL,CAAagC,iBAAb,CAA+BP,KAAK,CAACQ,SAArC;;AACA,QAAI,KAAKzB,KAAL,KAAerB,KAAK,CAAC+C,MAAzB,EAAiC;AAC/B;AACD;;AACD,SAAKd,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAKzB,OAAnD;;AAEA,QAAI,KAAKQ,KAAL,KAAerB,KAAK,CAAC+C,MAAzB,EAAiC;AAC/B,WAAKC,GAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;AAESC,EAAAA,eAAe,CAACZ,KAAD,EAA4B;AACnD,UAAMY,eAAN,CAAsBZ,KAAtB;AACA,SAAKL,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAKzB,OAAnD;AACA,SAAKA,OAAL,CAAagC,iBAAb,CAA+BP,KAAK,CAACQ,SAArC;;AAEA,QAAI,KAAKzB,KAAL,KAAerB,KAAK,CAAC+C,MAArB,IAA+B,KAAKlC,OAAL,CAAaC,oBAAb,GAAoC,CAAvE,EAA0E;AACxE,WAAKkC,GAAL;AACD;AACF;;AAESG,EAAAA,aAAa,CAACb,KAAD,EAA4B;AACjD,QAAI,KAAKzB,OAAL,CAAaC,oBAAb,GAAoC,CAAxC,EAA2C;AACzC;AACD;;AACD,SAAKD,OAAL,CAAauC,KAAb,CAAmBd,KAAnB;AAEA,SAAKL,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAKzB,OAAnD;AACA,UAAMsC,aAAN,CAAoBb,KAApB;AACD;;AACSe,EAAAA,oBAAoB,CAACf,KAAD,EAA4B;AACxD,QAAI,KAAKzB,OAAL,CAAaC,oBAAb,GAAoC,CAAxC,EAA2C;AACzC;AACD;;AACD,SAAKD,OAAL,CAAauC,KAAb,CAAmBd,KAAnB;AAEA,SAAKL,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAKzB,OAAnD;AACA,UAAMwC,oBAAN,CAA2Bf,KAA3B;AACD;;AAEOI,EAAAA,QAAQ,GAAS;AACvB,QAAI,KAAKrB,KAAL,KAAerB,KAAK,CAACsD,YAAzB,EAAuC;AACrC;AACD;;AAED,SAAKC,aAAL;AACA,SAAKC,KAAL;AACD;;AAEMjC,EAAAA,QAAQ,CAACkC,KAAD,EAAwB;AACrC,QAAI,KAAKpC,KAAL,KAAerB,KAAK,CAAC+C,MAAzB,EAAiC;AAC/B,WAAKQ,aAAL;AACD;;AAED,UAAMhC,QAAN,CAAekC,KAAf;AACD;;AAESC,EAAAA,OAAO,GAAS;AACxB,SAAKH,aAAL;AACD;;AAESA,EAAAA,aAAa,GAAS;AAC9B,QAAI,KAAKlC,KAAL,KAAerB,KAAK,CAAC+C,MAAzB,EAAiC;AAC/B;AACD;;AACD,SAAK9B,QAAL,GAAgB,CAAhB;AACA,SAAKN,KAAL,GAAa,CAAb;AACD;;AA7I6D", "sourcesContent": ["import { State } from '../../State';\nimport { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\nimport ScaleGestureDetector, {\n  ScaleGestureListener,\n} from '../detectors/ScaleGestureDetector';\n\nexport default class PinchGestureHandler extends GestureHandler {\n  private scale = 1;\n  private velocity = 0;\n\n  private startingSpan = 0;\n  private spanSlop = DEFAULT_TOUCH_SLOP;\n\n  private scaleDetectorListener: ScaleGestureListener = {\n    onScaleBegin: (detector: ScaleGestureDetector): boolean => {\n      this.startingSpan = detector.currentSpan;\n      return true;\n    },\n    onScale: (detector: ScaleGestureDetector): boolean => {\n      const prevScaleFactor: number = this.scale;\n      this.scale *= detector.calculateScaleFactor(\n        this.tracker.trackedPointersCount\n      );\n\n      const delta = detector.timeDelta;\n      if (delta > 0) {\n        this.velocity = (this.scale - prevScaleFactor) / delta;\n      }\n\n      if (\n        Math.abs(this.startingSpan - detector.currentSpan) >= this.spanSlop &&\n        this.state === State.BEGAN\n      ) {\n        this.activate();\n      }\n      return true;\n    },\n    onScaleEnd: (\n      _detector: ScaleGestureDetector\n      // eslint-disable-next-line @typescript-eslint/no-empty-function\n    ): void => {},\n  };\n\n  private scaleGestureDetector: ScaleGestureDetector = new ScaleGestureDetector(\n    this.scaleDetectorListener\n  );\n\n  public init(ref: number, propsRef: React.RefObject<unknown>) {\n    super.init(ref, propsRef);\n\n    this.shouldCancelWhenOutside = false;\n  }\n\n  protected transformNativeEvent() {\n    return {\n      focalX: this.scaleGestureDetector.focusX,\n      focalY: this.scaleGestureDetector.focusY,\n      velocity: this.velocity,\n      scale: this.scale,\n    };\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.tryBegin();\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n    if (this.state !== State.ACTIVE) {\n      return;\n    }\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n\n    if (this.state === State.ACTIVE) {\n      this.end();\n    } else {\n      this.fail();\n    }\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (this.state === State.ACTIVE && this.tracker.trackedPointersCount < 2) {\n      this.end();\n    }\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    if (this.tracker.trackedPointersCount < 2) {\n      return;\n    }\n    this.tracker.track(event);\n\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n    super.onPointerMove(event);\n  }\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    if (this.tracker.trackedPointersCount < 2) {\n      return;\n    }\n    this.tracker.track(event);\n\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n    super.onPointerOutOfBounds(event);\n  }\n\n  private tryBegin(): void {\n    if (this.state !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.resetProgress();\n    this.begin();\n  }\n\n  public activate(force?: boolean): void {\n    if (this.state !== State.ACTIVE) {\n      this.resetProgress();\n    }\n\n    super.activate(force);\n  }\n\n  protected onReset(): void {\n    this.resetProgress();\n  }\n\n  protected resetProgress(): void {\n    if (this.state === State.ACTIVE) {\n      return;\n    }\n    this.velocity = 0;\n    this.scale = 1;\n  }\n}\n"]}