{"version": 3, "sources": ["index.ts"], "names": ["default", "TouchableNativeFeedback", "TouchableWithoutFeedback", "TouchableOpacity", "TouchableHighlight"], "mappings": "AAGA,SAASA,OAAO,IAAIC,uBAApB,QAAmD,2BAAnD;AACA,SAASD,OAAO,IAAIE,wBAApB,QAAoD,4BAApD;AACA,SAASF,OAAO,IAAIG,gBAApB,QAA4C,oBAA5C;AACA,SAASH,OAAO,IAAII,kBAApB,QAA8C,sBAA9C", "sourcesContent": ["export type { TouchableHighlightProps } from './TouchableHighlight';\nexport type { TouchableOpacityProps } from './TouchableOpacity';\nexport type { TouchableWithoutFeedbackProps } from './TouchableWithoutFeedback';\nexport { default as TouchableNativeFeedback } from './TouchableNativeFeedback';\nexport { default as TouchableWithoutFeedback } from './TouchableWithoutFeedback';\nexport { default as TouchableOpacity } from './TouchableOpacity';\nexport { default as TouchableHighlight } from './TouchableHighlight';\n"]}