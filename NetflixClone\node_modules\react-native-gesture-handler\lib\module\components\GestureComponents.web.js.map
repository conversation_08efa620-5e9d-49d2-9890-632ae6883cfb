{"version": 3, "sources": ["GestureComponents.web.tsx"], "names": ["React", "FlatList", "RNFlatList", "Switch", "RNSwitch", "TextInput", "RNTextInput", "ScrollView", "RNScrollView", "View", "createNativeWrapper", "disallowInterruption", "shouldCancelWhenOutside", "shouldActivateOnStart", "DrawerLayoutAndroid", "console", "warn", "RefreshControl", "forwardRef", "props", "ref", "scrollProps"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SACEC,QAAQ,IAAIC,UADd,EAEEC,MAAM,IAAIC,QAFZ,EAGEC,SAAS,IAAIC,WAHf,EAIEC,UAAU,IAAIC,YAJhB,EAMEC,IANF,QAOO,cAPP;AASA,OAAOC,mBAAP,MAAgC,iCAAhC;AAEA,OAAO,MAAMH,UAAU,GAAGG,mBAAmB,CAACF,YAAD,EAAe;AAC1DG,EAAAA,oBAAoB,EAAE;AADoC,CAAf,CAAtC;AAIP,OAAO,MAAMR,MAAM,GAAGO,mBAAmB,CAACN,QAAD,EAAW;AAClDQ,EAAAA,uBAAuB,EAAE,KADyB;AAElDC,EAAAA,qBAAqB,EAAE,IAF2B;AAGlDF,EAAAA,oBAAoB,EAAE;AAH4B,CAAX,CAAlC;AAKP,OAAO,MAAMN,SAAS,GAAGK,mBAAmB,CAACJ,WAAD,CAArC;AACP,OAAO,MAAMQ,mBAAmB,GAAG,MAAM;AACvCC,EAAAA,OAAO,CAACC,IAAR,CAAa,8CAAb;AACA,sBAAO,oBAAC,IAAD,OAAP;AACD,CAHM,C,CAKP;AACA;AACA;;AACA,OAAO,MAAMC,cAAc,GAAGP,mBAAmB,CAACD,IAAD,CAA1C;AAEP,OAAO,MAAMR,QAAQ,gBAAGD,KAAK,CAACkB,UAAN,CACtB,CAAoBC,KAApB,EAAiDC,GAAjD,kBACE,oBAAC,UAAD;AACE,EAAA,GAAG,EAAEA;AADP,GAEMD,KAFN;AAGE,EAAA,qBAAqB,EAAGE,WAAD,iBAAiB,oBAAC,UAAD,EAAgBA,WAAhB;AAH1C,GAFoB,CAAjB", "sourcesContent": ["import * as React from 'react';\nimport {\n  FlatList as <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Switch as RNSwitch,\n  TextInput as RNTextInput,\n  ScrollView as RNScrollView,\n  FlatListProps,\n  View,\n} from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\n\nexport const ScrollView = createNativeWrapper(RNScrollView, {\n  disallowInterruption: false,\n});\n\nexport const Switch = createNativeWrapper(RNSwitch, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: true,\n  disallowInterruption: true,\n});\nexport const TextInput = createNativeWrapper(RNTextInput);\nexport const DrawerLayoutAndroid = () => {\n  console.warn('DrawerLayoutAndroid is not supported on web!');\n  return <View />;\n};\n\n// RefreshControl is implemented as a functional component, rendering a View\n// NativeViewGestureHandler needs to set a ref on its child, which cannot be done\n// on functional components\nexport const RefreshControl = createNativeWrapper(View);\n\nexport const FlatList = React.forwardRef(\n  <ItemT extends any>(props: FlatListProps<ItemT>, ref: any) => (\n    <RNFlatList\n      ref={ref}\n      {...props}\n      renderScrollComponent={(scrollProps) => <ScrollView {...scrollProps} />}\n    />\n  )\n);\n"]}