{"version": 3, "sources": ["NativeViewGestureHandler.ts"], "names": ["NativeViewGestureHandler", "Gesture<PERSON>andler", "DEFAULT_TOUCH_SLOP", "init", "ref", "propsRef", "shouldCancelWhenOutside", "Platform", "OS", "view", "delegate", "restoreViewStyles", "buttonRole", "getAttribute", "updateGestureConfig", "enabled", "props", "config", "shouldActivateOnStart", "undefined", "disallowInterruption", "style", "onPointerDown", "event", "tracker", "addToTracker", "newPointerAction", "tryToSendTouchEvent", "onPointerAdd", "lastCoords", "getAbsoluteCoordsAverage", "startX", "x", "startY", "y", "state", "State", "UNDETERMINED", "begin", "isRNGHText", "hasAttribute", "activate", "onPointerMove", "track", "dx", "dy", "distSq", "minDistSq", "ACTIVE", "cancel", "BEGAN", "onPointerLeave", "onPointerUp", "onUp", "onPointerRemove", "removeFromTracker", "pointerId", "trackedPointersCount", "end", "fail", "shouldRecognizeSimultaneously", "handler", "disallowsInterruption", "canBeInterrupted", "handlerTag", "shouldBeCancelledByOther", "_handler", "isButton"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAGA;;;;;;AACe,MAAMA,wBAAN,SAAuCC,uBAAvC,CAAsD;AAAA;AAAA;;AAAA;;AAAA,mDAKnC,KALmC;;AAAA,kDAMpC,KANoC;;AAAA,oCAQlD,CARkD;;AAAA,oCASlD,CATkD;;AAAA,uCAU/CC,gCAAqBA,6BAV0B;AAAA;;AAY5DC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAwD;AACjE,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AAEA,SAAKC,uBAAL,GAA+B,IAA/B;;AAEA,QAAIC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,UAAMC,IAAI,GAAG,KAAKC,QAAL,CAAcD,IAA3B;AAEA,SAAKE,iBAAL,CAAuBF,IAAvB;AACA,SAAKG,UAAL,GAAkBH,IAAI,CAACI,YAAL,CAAkB,MAAlB,MAA8B,QAAhD;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;;AAEA,QAAI,KAAKC,MAAL,CAAYC,qBAAZ,KAAsCC,SAA1C,EAAqD;AACnD,WAAKD,qBAAL,GAA6B,KAAKD,MAAL,CAAYC,qBAAzC;AACD;;AACD,QAAI,KAAKD,MAAL,CAAYG,oBAAZ,KAAqCD,SAAzC,EAAoD;AAClD,WAAKC,oBAAL,GAA4B,KAAKH,MAAL,CAAYG,oBAAxC;AACD;;AAED,UAAMX,IAAI,GAAG,KAAKC,QAAL,CAAcD,IAA3B;AACA,SAAKE,iBAAL,CAAuBF,IAAvB;AACD;;AAEOE,EAAAA,iBAAiB,CAACF,IAAD,EAAoB;AAC3C,QAAI,CAACA,IAAL,EAAW;AACT;AACD;;AAEDA,IAAAA,IAAI,CAACY,KAAL,CAAW,aAAX,IAA4B,MAA5B,CAL2C,CAM3C;;AACAZ,IAAAA,IAAI,CAACY,KAAL,CAAW,oBAAX,IAAmC,MAAnC;AACD;;AAESC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AACA,SAAKG,gBAAL;AAEA,SAAKC,mBAAL,CAAyBJ,KAAzB;AACD;;AAESK,EAAAA,YAAY,CAACL,KAAD,EAA4B;AAChD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMK,YAAN,CAAmBL,KAAnB;AACA,SAAKG,gBAAL;AACD;;AAEOA,EAAAA,gBAAgB,GAAS;AAC/B,UAAMG,UAAU,GAAG,KAAKL,OAAL,CAAaM,wBAAb,EAAnB;AACA,SAAKC,MAAL,GAAcF,UAAU,CAACG,CAAzB;AACA,SAAKC,MAAL,GAAcJ,UAAU,CAACK,CAAzB;;AAEA,QAAI,KAAKC,KAAL,KAAeC,aAAMC,YAAzB,EAAuC;AACrC;AACD;;AAED,SAAKC,KAAL;AAEA,UAAM7B,IAAI,GAAG,KAAKC,QAAL,CAAcD,IAA3B;AACA,UAAM8B,UAAU,GAAG9B,IAAI,CAAC+B,YAAL,CAAkB,UAAlB,CAAnB;;AAEA,QAAI,KAAK5B,UAAL,IAAmB2B,UAAvB,EAAmC;AACjC,WAAKE,QAAL;AACD;AACF;;AAESC,EAAAA,aAAa,CAACnB,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAamB,KAAb,CAAmBpB,KAAnB;AAEA,UAAMM,UAAU,GAAG,KAAKL,OAAL,CAAaM,wBAAb,EAAnB;AACA,UAAMc,EAAE,GAAG,KAAKb,MAAL,GAAcF,UAAU,CAACG,CAApC;AACA,UAAMa,EAAE,GAAG,KAAKZ,MAAL,GAAcJ,UAAU,CAACK,CAApC;AACA,UAAMY,MAAM,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA9B;;AAEA,QAAIC,MAAM,IAAI,KAAKC,SAAnB,EAA8B;AAC5B,UAAI,KAAKnC,UAAL,IAAmB,KAAKuB,KAAL,KAAeC,aAAMY,MAA5C,EAAoD;AAClD,aAAKC,MAAL;AACD,OAFD,MAEO,IAAI,CAAC,KAAKrC,UAAN,IAAoB,KAAKuB,KAAL,KAAeC,aAAMc,KAA7C,EAAoD;AACzD,aAAKT,QAAL;AACD;AACF;AACF;;AAESU,EAAAA,cAAc,GAAS;AAC/B,QAAI,KAAKhB,KAAL,KAAeC,aAAMc,KAArB,IAA8B,KAAKf,KAAL,KAAeC,aAAMY,MAAvD,EAA+D;AAC7D,WAAKC,MAAL;AACD;AACF;;AAESG,EAAAA,WAAW,CAAC7B,KAAD,EAA4B;AAC/C,UAAM6B,WAAN,CAAkB7B,KAAlB;AACA,SAAK8B,IAAL,CAAU9B,KAAV;AACD;;AAES+B,EAAAA,eAAe,CAAC/B,KAAD,EAA4B;AACnD,UAAM+B,eAAN,CAAsB/B,KAAtB;AACA,SAAK8B,IAAL,CAAU9B,KAAV;AACD;;AAEO8B,EAAAA,IAAI,CAAC9B,KAAD,EAA4B;AACtC,SAAKC,OAAL,CAAa+B,iBAAb,CAA+BhC,KAAK,CAACiC,SAArC;;AAEA,QAAI,KAAKhC,OAAL,CAAaiC,oBAAb,KAAsC,CAA1C,EAA6C;AAC3C,UAAI,KAAKtB,KAAL,KAAeC,aAAMY,MAAzB,EAAiC;AAC/B,aAAKU,GAAL;AACD,OAFD,MAEO;AACL,aAAKC,IAAL;AACD;AACF;AACF;;AAEMC,EAAAA,6BAA6B,CAACC,OAAD,EAAmC;AACrE,QAAI,MAAMD,6BAAN,CAAoCC,OAApC,CAAJ,EAAkD;AAChD,aAAO,IAAP;AACD;;AAED,QACEA,OAAO,YAAY7D,wBAAnB,IACA6D,OAAO,CAAC1B,KAAR,KAAkBC,aAAMY,MADxB,IAEAa,OAAO,CAACC,qBAAR,EAHF,EAIE;AACA,aAAO,KAAP;AACD;;AAED,UAAMC,gBAAgB,GAAG,CAAC,KAAK3C,oBAA/B;;AAEA,QACE,KAAKe,KAAL,KAAeC,aAAMY,MAArB,IACAa,OAAO,CAAC1B,KAAR,KAAkBC,aAAMY,MADxB,IAEAe,gBAHF,EAIE;AACA,aAAO,KAAP;AACD;;AAED,WACE,KAAK5B,KAAL,KAAeC,aAAMY,MAArB,IAA+Be,gBAA/B,IAAmDF,OAAO,CAACG,UAAR,GAAqB,CAD1E;AAGD;;AAEMC,EAAAA,wBAAwB,CAACC,QAAD,EAAoC;AACjE,WAAO,CAAC,KAAK9C,oBAAb;AACD;;AAEM0C,EAAAA,qBAAqB,GAAY;AACtC,WAAO,KAAK1C,oBAAZ;AACD;;AAEM+C,EAAAA,QAAQ,GAAY;AACzB,WAAO,KAAKvD,UAAZ;AACD;;AAvKkE", "sourcesContent": ["import { Platform } from 'react-native';\nimport { State } from '../../State';\nimport { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\nexport default class NativeViewGestureHandler extends GestureHandler {\n  private buttonRole!: boolean;\n\n  // TODO: Implement logic for activation on start\n  // @ts-ignore Logic yet to be implemented\n  private shouldActivateOnStart = false;\n  private disallowInterruption = false;\n\n  private startX = 0;\n  private startY = 0;\n  private minDistSq = DEFAULT_TOUCH_SLOP * DEFAULT_TOUCH_SLOP;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>): void {\n    super.init(ref, propsRef);\n\n    this.shouldCancelWhenOutside = true;\n\n    if (Platform.OS !== 'web') {\n      return;\n    }\n\n    const view = this.delegate.view as HTMLElement;\n\n    this.restoreViewStyles(view);\n    this.buttonRole = view.getAttribute('role') === 'button';\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n\n    if (this.config.shouldActivateOnStart !== undefined) {\n      this.shouldActivateOnStart = this.config.shouldActivateOnStart;\n    }\n    if (this.config.disallowInterruption !== undefined) {\n      this.disallowInterruption = this.config.disallowInterruption;\n    }\n\n    const view = this.delegate.view as HTMLElement;\n    this.restoreViewStyles(view);\n  }\n\n  private restoreViewStyles(view: HTMLElement) {\n    if (!view) {\n      return;\n    }\n\n    view.style['touchAction'] = 'auto';\n    // @ts-ignore Turns on defualt touch behavior on Safari\n    view.style['WebkitTouchCallout'] = 'auto';\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n    this.newPointerAction();\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.newPointerAction();\n  }\n\n  private newPointerAction(): void {\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.startX = lastCoords.x;\n    this.startY = lastCoords.y;\n\n    if (this.state !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.begin();\n\n    const view = this.delegate.view as HTMLElement;\n    const isRNGHText = view.hasAttribute('rnghtext');\n\n    if (this.buttonRole || isRNGHText) {\n      this.activate();\n    }\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    const dx = this.startX - lastCoords.x;\n    const dy = this.startY - lastCoords.y;\n    const distSq = dx * dx + dy * dy;\n\n    if (distSq >= this.minDistSq) {\n      if (this.buttonRole && this.state === State.ACTIVE) {\n        this.cancel();\n      } else if (!this.buttonRole && this.state === State.BEGAN) {\n        this.activate();\n      }\n    }\n  }\n\n  protected onPointerLeave(): void {\n    if (this.state === State.BEGAN || this.state === State.ACTIVE) {\n      this.cancel();\n    }\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.onUp(event);\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.onUp(event);\n  }\n\n  private onUp(event: AdaptedEvent): void {\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (this.tracker.trackedPointersCount === 0) {\n      if (this.state === State.ACTIVE) {\n        this.end();\n      } else {\n        this.fail();\n      }\n    }\n  }\n\n  public shouldRecognizeSimultaneously(handler: GestureHandler): boolean {\n    if (super.shouldRecognizeSimultaneously(handler)) {\n      return true;\n    }\n\n    if (\n      handler instanceof NativeViewGestureHandler &&\n      handler.state === State.ACTIVE &&\n      handler.disallowsInterruption()\n    ) {\n      return false;\n    }\n\n    const canBeInterrupted = !this.disallowInterruption;\n\n    if (\n      this.state === State.ACTIVE &&\n      handler.state === State.ACTIVE &&\n      canBeInterrupted\n    ) {\n      return false;\n    }\n\n    return (\n      this.state === State.ACTIVE && canBeInterrupted && handler.handlerTag > 0\n    );\n  }\n\n  public shouldBeCancelledByOther(_handler: GestureHandler): boolean {\n    return !this.disallowInterruption;\n  }\n\n  public disallowsInterruption(): boolean {\n    return this.disallowInterruption;\n  }\n\n  public isButton(): boolean {\n    return this.buttonRole;\n  }\n}\n"]}