{"version": 3, "sources": ["gestureStateManager.ts"], "names": ["warningMessage", "REANIMATED_AVAILABLE", "useSharedValue", "undefined", "setGestureState", "Reanimated", "create", "handlerTag", "begin", "State", "BEGAN", "console", "warn", "activate", "ACTIVE", "fail", "FAILED", "end", "END", "GestureStateManager"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AASA,MAAMA,cAAc,GAAG,uBACrB,kFADqB,CAAvB,C,CAIA;AACA;;AACA,MAAMC,oBAAoB,GAAG,6HAAYC,cAAZ,MAA+BC,SAA5D;AACA,MAAMC,eAAe,GAAGC,6BAAH,aAAGA,6BAAH,uBAAGA,8BAAYD,eAApC;;AAEA,SAASE,MAAT,CAAgBC,UAAhB,EAA6D;AAC3D;;AACA,SAAO;AACLC,IAAAA,KAAK,EAAE,MAAM;AACX;;AACA,UAAIP,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEG,UAAF,EAAcE,aAAMC,KAApB,CAAf;AACD,OAJD,MAIO;AACLC,QAAAA,OAAO,CAACC,IAAR,CAAaZ,cAAb;AACD;AACF,KAVI;AAYLa,IAAAA,QAAQ,EAAE,MAAM;AACd;;AACA,UAAIZ,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEG,UAAF,EAAcE,aAAMK,MAApB,CAAf;AACD,OAJD,MAIO;AACLH,QAAAA,OAAO,CAACC,IAAR,CAAaZ,cAAb;AACD;AACF,KArBI;AAuBLe,IAAAA,IAAI,EAAE,MAAM;AACV;;AACA,UAAId,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEG,UAAF,EAAcE,aAAMO,MAApB,CAAf;AACD,OAJD,MAIO;AACLL,QAAAA,OAAO,CAACC,IAAR,CAAaZ,cAAb;AACD;AACF,KAhCI;AAkCLiB,IAAAA,GAAG,EAAE,MAAM;AACT;;AACA,UAAIhB,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEG,UAAF,EAAcE,aAAMS,GAApB,CAAf;AACD,OAJD,MAIO;AACLP,QAAAA,OAAO,CAACC,IAAR,CAAaZ,cAAb;AACD;AACF;AA3CI,GAAP;AA6CD;;AAEM,MAAMmB,mBAAmB,GAAG;AACjCb,EAAAA;AADiC,CAA5B", "sourcesContent": ["import { Reanimated } from './reanimatedWrapper';\nimport { State } from '../../State';\nimport { tagMessage } from '../../utils';\n\nexport interface GestureStateManagerType {\n  begin: () => void;\n  activate: () => void;\n  fail: () => void;\n  end: () => void;\n}\n\nconst warningMessage = tagMessage(\n  'react-native-reanimated is required in order to use synchronous state management'\n);\n\n// Check if reanimated module is available, but look for useSharedValue as conditional\n// require of reanimated can sometimes return content of `utils.ts` file (?)\nconst REANIMATED_AVAILABLE = Reanimated?.useSharedValue !== undefined;\nconst setGestureState = Reanimated?.setGestureState;\n\nfunction create(handlerTag: number): GestureStateManagerType {\n  'worklet';\n  return {\n    begin: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.BEGAN);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n\n    activate: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.ACTIVE);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n\n    fail: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.FAILED);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n\n    end: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.END);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n  };\n}\n\nexport const GestureStateManager = {\n  create,\n};\n"]}