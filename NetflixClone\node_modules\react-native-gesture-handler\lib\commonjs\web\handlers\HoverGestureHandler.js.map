{"version": 3, "sources": ["HoverGestureHandler.ts"], "names": ["HoverGestureHandler", "Gesture<PERSON>andler", "transformNativeEvent", "stylusData", "onPointerMoveOver", "event", "GestureHandlerOrchestrator", "instance", "recordHandlerIfNotPresent", "tracker", "addToTracker", "state", "State", "UNDETERMINED", "begin", "activate", "onPointerMoveOut", "removeFromTracker", "pointerId", "end", "onPointerMove", "track", "onPointerCancel", "reset"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA;;;;;;AAEe,MAAMA,mBAAN,SAAkCC,uBAAlC,CAAiD;AAAA;AAAA;;AAAA;AAAA;;AAGpDC,EAAAA,oBAAoB,GAA4B;AACxD,WAAO,EACL,GAAG,MAAMA,oBAAN,EADE;AAELC,MAAAA,UAAU,EAAE,KAAKA;AAFZ,KAAP;AAID;;AAESC,EAAAA,iBAAiB,CAACC,KAAD,EAA4B;AACrDC,wCAA2BC,QAA3B,CAAoCC,yBAApC,CAA8D,IAA9D;;AAEA,SAAKC,OAAL,CAAaC,YAAb,CAA0BL,KAA1B;AACA,SAAKF,UAAL,GAAkBE,KAAK,CAACF,UAAxB;AACA,UAAMC,iBAAN,CAAwBC,KAAxB;;AAEA,QAAI,KAAKM,KAAL,KAAeC,aAAMC,YAAzB,EAAuC;AACrC,WAAKC,KAAL;AACA,WAAKC,QAAL;AACD;AACF;;AAESC,EAAAA,gBAAgB,CAACX,KAAD,EAA4B;AACpD,SAAKI,OAAL,CAAaQ,iBAAb,CAA+BZ,KAAK,CAACa,SAArC;AACA,SAAKf,UAAL,GAAkBE,KAAK,CAACF,UAAxB;AAEA,UAAMa,gBAAN,CAAuBX,KAAvB;AAEA,SAAKc,GAAL;AACD;;AAESC,EAAAA,aAAa,CAACf,KAAD,EAA4B;AACjD,SAAKI,OAAL,CAAaY,KAAb,CAAmBhB,KAAnB;AACA,SAAKF,UAAL,GAAkBE,KAAK,CAACF,UAAxB;AAEA,UAAMiB,aAAN,CAAoBf,KAApB;AACD;;AAESiB,EAAAA,eAAe,CAACjB,KAAD,EAA4B;AACnD,UAAMiB,eAAN,CAAsBjB,KAAtB;AACA,SAAKkB,KAAL;AACD;;AA1C6D", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent, StylusData } from '../interfaces';\nimport GestureHandlerOrchestrator from '../tools/GestureHandlerOrchestrator';\nimport GestureHandler from './GestureHandler';\n\nexport default class HoverGestureHandler extends GestureHandler {\n  private stylusData: StylusData | undefined;\n\n  protected transformNativeEvent(): Record<string, unknown> {\n    return {\n      ...super.transformNativeEvent(),\n      stylusData: this.stylusData,\n    };\n  }\n\n  protected onPointerMoveOver(event: AdaptedEvent): void {\n    GestureHandlerOrchestrator.instance.recordHandlerIfNotPresent(this);\n\n    this.tracker.addToTracker(event);\n    this.stylusData = event.stylusData;\n    super.onPointerMoveOver(event);\n\n    if (this.state === State.UNDETERMINED) {\n      this.begin();\n      this.activate();\n    }\n  }\n\n  protected onPointerMoveOut(event: AdaptedEvent): void {\n    this.tracker.removeFromTracker(event.pointerId);\n    this.stylusData = event.stylusData;\n\n    super.onPointerMoveOut(event);\n\n    this.end();\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    this.stylusData = event.stylusData;\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerCancel(event: AdaptedEvent): void {\n    super.onPointerCancel(event);\n    this.reset();\n  }\n}\n"]}