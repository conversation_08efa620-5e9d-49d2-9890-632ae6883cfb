{"version": 3, "sources": ["PlatformConstants.ts"], "names": ["NativeModules", "PlatformConstants", "Platform", "constants"], "mappings": ";;;;;;;AAAA;;;;wCAMgBA,0B,aAAAA,0B,uBAAAA,2BAAeC,iB,yEAC7BC,sBAASC,S", "sourcesContent": ["import { NativeModules, Platform } from 'react-native';\n\ntype PlatformConstants = {\n  forceTouchAvailable: boolean;\n};\n\nexport default (NativeModules?.PlatformConstants ??\n  Platform.constants) as PlatformConstants;\n"]}