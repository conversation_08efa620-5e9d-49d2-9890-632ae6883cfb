{"version": 3, "sources": ["getReactNativeVersion.ts"], "names": ["pack", "majorStr", "minorStr", "version", "split", "REACT_NATIVE_VERSION", "major", "parseInt", "minor", "getReactNativeVersion"], "mappings": "AAAA,OAAOA,IAAP,MAAiB,2BAAjB;AAEA,MAAM,CAACC,QAAD,EAAWC,QAAX,IAAuBF,IAAI,CAACG,OAAL,CAAaC,KAAb,CAAmB,GAAnB,CAA7B;AACA,MAAMC,oBAAoB,GAAG;AAC3BC,EAAAA,KAAK,EAAEC,QAAQ,CAACN,QAAD,EAAW,EAAX,CADY;AAE3BO,EAAAA,KAAK,EAAED,QAAQ,CAACL,QAAD,EAAW,EAAX;AAFY,CAA7B;AAKA,OAAO,SAASO,qBAAT,GAAiC;AACtC,SAAOJ,oBAAP;AACD", "sourcesContent": ["import pack from 'react-native/package.json';\n\nconst [majorStr, minorStr] = pack.version.split('.');\nconst REACT_NATIVE_VERSION = {\n  major: parseInt(majorStr, 10),\n  minor: parseInt(minorStr, 10),\n};\n\nexport function getReactNativeVersion() {\n  return REACT_NATIVE_VERSION;\n}\n"]}