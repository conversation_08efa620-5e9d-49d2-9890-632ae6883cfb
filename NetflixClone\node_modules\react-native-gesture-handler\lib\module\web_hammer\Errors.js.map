{"version": 3, "sources": ["Errors.ts"], "names": ["GesturePropError", "Error", "constructor", "name", "value", "expectedType"], "mappings": "AAAA,OAAO,MAAMA,gBAAN,SAA+BC,KAA/B,CAAqC;AAC1CC,EAAAA,WAAW,CAACC,IAAD,EAAeC,KAAf,EAA+BC,YAA/B,EAAqD;AAC9D,UACG,sBAAqBF,IAAK,KAAIC,KAAM,iBAAgBC,YAAa,IADpE;AAGD;;AALyC", "sourcesContent": ["export class GesturePropError extends Error {\n  constructor(name: string, value: unknown, expectedType: string) {\n    super(\n      `Invalid property \\`${name}: ${value}\\` expected \\`${expectedType}\\``\n    );\n  }\n}\n"]}