package com.swmansion.gesturehandler.core

interface GestureHandlerInteractionController {
  fun shouldWaitForHandlerFailure(handler: Gesture<PERSON>and<PERSON><*>, otherHandler: GestureHandler<*>): Boolean
  fun shouldRequireHandlerToWaitForFailure(handler: <PERSON>esture<PERSON><PERSON><PERSON><*>, otherHandler: <PERSON>esture<PERSON><PERSON><PERSON><*>): Boolean
  fun shouldRecognizeSimultaneously(handler: Gesture<PERSON>and<PERSON><*>, otherHandler: Gesture<PERSON>andler<*>): Boolean
  fun shouldHandlerBeCancelledBy(handler: GestureHand<PERSON><*>, otherHandler: <PERSON>esture<PERSON><PERSON><PERSON><*>): <PERSON><PERSON><PERSON>
}
