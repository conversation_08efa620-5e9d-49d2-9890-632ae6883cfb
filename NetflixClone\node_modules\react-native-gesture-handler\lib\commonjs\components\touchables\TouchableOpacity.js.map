{"version": 3, "sources": ["TouchableOpacity.tsx"], "names": ["TouchableOpacity", "Component", "childStyle", "StyleSheet", "flatten", "props", "style", "opacity", "valueOf", "Animated", "Value", "getChildStyleOpacityWithDefault", "value", "duration", "timing", "toValue", "easing", "Easing", "inOut", "quad", "useNativeDriver", "useNativeAnimations", "start", "_from", "to", "TOUCHABLE_STATE", "BEGAN", "setOpacityTo", "activeOpacity", "UNDETERMINED", "MOVED_OUTSIDE", "render", "rest", "onStateChange", "children", "GenericTouchable", "defaultProps"], "mappings": ";;;;;;;AAAA;;AAOA;;AAEA;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACe,MAAMA,gBAAN,SAA+BC,eAA/B,CAAgE;AAAA;AAAA;;AAAA,6DAO3C,MAAM;AACtC,YAAMC,UAAU,GAAGC,wBAAWC,OAAX,CAAmB,KAAKC,KAAL,CAAWC,KAA9B,KAAwC,EAA3D;AACA,aAAOJ,UAAU,CAACK,OAAX,IAAsB,IAAtB,GACH,CADG,GAEFL,UAAU,CAACK,OAAX,CAAmBC,OAAnB,EAFL;AAGD,KAZ4E;;AAAA,qCAcnE,IAAIC,sBAASC,KAAb,CAAmB,KAAKC,+BAAL,EAAnB,CAdmE;;AAAA,0CAgB9D,CAACC,KAAD,EAAgBC,QAAhB,KAAqC;AAAA;;AAClDJ,4BAASK,MAAT,CAAgB,KAAKP,OAArB,EAA8B;AAC5BQ,QAAAA,OAAO,EAAEH,KADmB;AAE5BC,QAAAA,QAAQ,EAAEA,QAFkB;AAG5BG,QAAAA,MAAM,EAAEC,oBAAOC,KAAP,CAAaD,oBAAOE,IAApB,CAHoB;AAI5BC,QAAAA,eAAe,2BAAE,KAAKf,KAAL,CAAWgB,mBAAb,yEAAoC;AAJvB,OAA9B,EAKGC,KALH;AAMD,KAvB4E;;AAAA,2CAyB7D,CAACC,KAAD,EAAgBC,EAAhB,KAA+B;AAC7C,UAAIA,EAAE,KAAKC,kCAAgBC,KAA3B,EAAkC;AAChC,aAAKC,YAAL,CAAkB,KAAKtB,KAAL,CAAWuB,aAA7B,EAA6C,CAA7C;AACD,OAFD,MAEO,IACLJ,EAAE,KAAKC,kCAAgBI,YAAvB,IACAL,EAAE,KAAKC,kCAAgBK,aAFlB,EAGL;AACA,aAAKH,YAAL,CAAkB,KAAKhB,+BAAL,EAAlB,EAA0D,GAA1D;AACD;AACF,KAlC4E;AAAA;;AAoC7EoB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEzB,MAAAA,KAAK,GAAG,EAAV;AAAc,SAAG0B;AAAjB,QAA0B,KAAK3B,KAArC;AACA,wBACE,oBAAC,yBAAD,eACM2B,IADN;AAEE,MAAA,KAAK,EAAE,CACL1B,KADK,EAEL;AACEC,QAAAA,OAAO,EAAE,KAAKA,OADhB,CAC8C;;AAD9C,OAFK,CAFT;AAQE,MAAA,aAAa,EAAE,KAAK0B;AARtB,QASG,KAAK5B,KAAL,CAAW6B,QAAX,GAAsB,KAAK7B,KAAL,CAAW6B,QAAjC,gBAA4C,oBAAC,iBAAD,OAT/C,CADF;AAaD;;AAnD4E;;;;gBAA1DlC,gB,kBACG,EACpB,GAAGmC,0BAAiBC,YADA;AAEpBR,EAAAA,aAAa,EAAE;AAFK,C", "sourcesContent": ["import {\n  Animated,\n  Easing,\n  StyleSheet,\n  View,\n  TouchableOpacityProps as RNTouchableOpacityProps,\n} from 'react-native';\nimport GenericTouchable, { TOUCHABLE_STATE } from './GenericTouchable';\nimport type { GenericTouchableProps } from './GenericTouchableProps';\nimport * as React from 'react';\nimport { Component } from 'react';\n\n/**\n * @deprecated TouchableOpacity will be removed in the future version of Gesture Handler. Use Pressable instead.\n */\nexport type TouchableOpacityProps = RNTouchableOpacityProps &\n  GenericTouchableProps & {\n    useNativeAnimations?: boolean;\n  };\n\n/**\n * @deprecated TouchableOpacity will be removed in the future version of Gesture Handler. Use Pressable instead.\n *\n * TouchableOpacity bases on timing animation which has been used in RN's core\n */\nexport default class TouchableOpacity extends Component<TouchableOpacityProps> {\n  static defaultProps = {\n    ...GenericTouchable.defaultProps,\n    activeOpacity: 0.2,\n  };\n\n  // Opacity is 1 one by default but could be overwritten\n  getChildStyleOpacityWithDefault = () => {\n    const childStyle = StyleSheet.flatten(this.props.style) || {};\n    return childStyle.opacity == null\n      ? 1\n      : (childStyle.opacity.valueOf() as number);\n  };\n\n  opacity = new Animated.Value(this.getChildStyleOpacityWithDefault());\n\n  setOpacityTo = (value: number, duration: number) => {\n    Animated.timing(this.opacity, {\n      toValue: value,\n      duration: duration,\n      easing: Easing.inOut(Easing.quad),\n      useNativeDriver: this.props.useNativeAnimations ?? true,\n    }).start();\n  };\n\n  onStateChange = (_from: number, to: number) => {\n    if (to === TOUCHABLE_STATE.BEGAN) {\n      this.setOpacityTo(this.props.activeOpacity!, 0);\n    } else if (\n      to === TOUCHABLE_STATE.UNDETERMINED ||\n      to === TOUCHABLE_STATE.MOVED_OUTSIDE\n    ) {\n      this.setOpacityTo(this.getChildStyleOpacityWithDefault(), 150);\n    }\n  };\n\n  render() {\n    const { style = {}, ...rest } = this.props;\n    return (\n      <GenericTouchable\n        {...rest}\n        style={[\n          style,\n          {\n            opacity: this.opacity as unknown as number, // TODO: fix this\n          },\n        ]}\n        onStateChange={this.onStateChange}>\n        {this.props.children ? this.props.children : <View />}\n      </GenericTouchable>\n    );\n  }\n}\n"]}