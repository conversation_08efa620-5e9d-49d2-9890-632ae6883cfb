{"version": 3, "sources": ["ReanimatedSwipeable.tsx"], "names": ["React", "forwardRef", "useCallback", "useImperativeHandle", "useMemo", "GestureObjects", "Gesture", "GestureDetector", "Animated", "ReduceMotion", "interpolate", "measure", "runOnJS", "runOnUI", "useAnimatedRef", "useAnimatedStyle", "useSharedValue", "with<PERSON><PERSON><PERSON>", "I18nManager", "StyleSheet", "View", "DRAG_TOSS", "SwipeDirection", "Swipeable", "props", "ref", "defaultProps", "friction", "overshootFriction", "dragOffset", "enableTrackpadTwoFingerGesture", "leftThreshold", "rightT<PERSON><PERSON><PERSON>", "enabled", "containerStyle", "childrenContainerStyle", "animationOptions", "overshootLeft", "overshootRight", "testID", "children", "dragOffsetFromLeftEdge", "dragOffsetFromRightEdge", "onSwipeableOpenStartDrag", "onSwipeableCloseStartDrag", "onSwipeableWillOpen", "onSwipeableWillClose", "onSwipeableOpen", "onSwipeableClose", "renderLeftActions", "renderRightActions", "simultaneousWithExternalGesture", "remainingProps", "rowState", "userDrag", "appliedTranslation", "row<PERSON>id<PERSON>", "leftWidth", "rightWidth", "showLeftProgress", "showRightProgress", "updateAnimatedEvent", "shouldOvershootLeft", "value", "shouldOvershootRight", "startOffset", "offsetDrag", "dispatchImmediateEvents", "fromValue", "toValue", "RIGHT", "LEFT", "dispatchEndEvents", "animateRow", "velocityX", "translationSpringConfig", "mass", "damping", "stiffness", "velocity", "overshootClamping", "reduceMotion", "System", "isClosing", "moveToRight", "usedWidth", "progressSpringConfig", "restDisplacementThreshold", "restSpeedThreshold", "frozenRowState", "isFinished", "progressTarget", "Math", "sign", "max", "leftLayoutRef", "leftWrapperLayoutRef", "rightLayoutRef", "updateElementWidths", "leftLayout", "leftWrapperLayout", "rightLayout", "pageX", "swipeableMethods", "close", "_WORKLET", "openLeft", "openRight", "reset", "onRowLayout", "nativeEvent", "layout", "width", "leftActionAnimation", "opacity", "leftElement", "styles", "leftActions", "rightActionAnimation", "rightElement", "rightActions", "handleRelease", "event", "translationX", "leftThresholdProp", "rightThresholdProp", "dragStarted", "tapGesture", "tap", "Tap", "shouldCancelWhenOutside", "onStart", "Array", "isArray", "panGesture", "pan", "Pan", "activeOffsetX", "onUpdate", "direction", "onEnd", "onFinalize", "animatedStyle", "transform", "translateX", "pointerEvents", "swipeableComponent", "container", "create", "overflow", "absoluteFillObject", "flexDirection", "isRTL"], "mappings": ";;AAAA;AACA;AACA;AAEA,OAAOA,KAAP,IAEEC,UAFF,EAGEC,WAHF,EAIEC,mBAJF,EAKEC,OALF,QAMO,OANP;AAQA,SAASC,cAAc,IAAIC,OAA3B,QAA0C,qCAA1C;AACA,SAASC,eAAT,QAAgC,sCAAhC;AAOA,OAAOC,QAAP,IACEC,YADF,EAGEC,WAHF,EAIEC,OAJF,EAKEC,OALF,EAMEC,OANF,EAOEC,cAPF,EAQEC,gBARF,EASEC,cATF,EAUEC,UAVF,QAWO,yBAXP;AAYA,SACEC,WADF,EAIEC,UAJF,EAKEC,IALF,QAOO,cAPP;AASA,MAAMC,SAAS,GAAG,IAAlB;IAOKC,c;;WAAAA,c;AAAAA,EAAAA,c;AAAAA,EAAAA,c;GAAAA,c,KAAAA,c;;AA8KL,MAAMC,SAAS,gBAAGtB,UAAU,CAC1B,SAASsB,SAAT,CACEC,KADF,EAEEC,GAFF,EAGE;AACA,QAAMC,YAAY,GAAG;AACnBC,IAAAA,QAAQ,EAAE,CADS;AAEnBC,IAAAA,iBAAiB,EAAE,CAFA;AAGnBC,IAAAA,UAAU,EAAE,EAHO;AAInBC,IAAAA,8BAA8B,EAAE;AAJb,GAArB;AAOA,QAAM;AACJC,IAAAA,aADI;AAEJC,IAAAA,cAFI;AAGJC,IAAAA,OAHI;AAIJC,IAAAA,cAJI;AAKJC,IAAAA,sBALI;AAMJC,IAAAA,gBANI;AAOJC,IAAAA,aAPI;AAQJC,IAAAA,cARI;AASJC,IAAAA,MATI;AAUJC,IAAAA,QAVI;AAWJV,IAAAA,8BAA8B,GAAGJ,YAAY,CAACI,8BAX1C;AAYJW,IAAAA,sBAAsB,GAAGf,YAAY,CAACG,UAZlC;AAaJa,IAAAA,uBAAuB,GAAGhB,YAAY,CAACG,UAbnC;AAcJF,IAAAA,QAAQ,GAAGD,YAAY,CAACC,QAdpB;AAeJC,IAAAA,iBAAiB,GAAGF,YAAY,CAACE,iBAf7B;AAgBJe,IAAAA,wBAhBI;AAiBJC,IAAAA,yBAjBI;AAkBJC,IAAAA,mBAlBI;AAmBJC,IAAAA,oBAnBI;AAoBJC,IAAAA,eApBI;AAqBJC,IAAAA,gBArBI;AAsBJC,IAAAA,iBAtBI;AAuBJC,IAAAA,kBAvBI;AAwBJC,IAAAA,+BAxBI;AAyBJ,OAAGC;AAzBC,MA0BF5B,KA1BJ;AA4BA,QAAM6B,QAAQ,GAAGrC,cAAc,CAAS,CAAT,CAA/B;AAEA,QAAMsC,QAAQ,GAAGtC,cAAc,CAAS,CAAT,CAA/B;AAEA,QAAMuC,kBAAkB,GAAGvC,cAAc,CAAS,CAAT,CAAzC;AAEA,QAAMwC,QAAQ,GAAGxC,cAAc,CAAS,CAAT,CAA/B;AACA,QAAMyC,SAAS,GAAGzC,cAAc,CAAS,CAAT,CAAhC;AACA,QAAM0C,UAAU,GAAG1C,cAAc,CAAS,CAAT,CAAjC;AAEA,QAAM2C,gBAAgB,GAAG3C,cAAc,CAAS,CAAT,CAAvC;AACA,QAAM4C,iBAAiB,GAAG5C,cAAc,CAAS,CAAT,CAAxC;AAEA,QAAM6C,mBAAmB,GAAG3D,WAAW,CAAC,MAAM;AAC5C;;AAEA,UAAM4D,mBAAmB,GAAGzB,aAAH,aAAGA,aAAH,cAAGA,aAAH,GAAoBoB,SAAS,CAACM,KAAV,GAAkB,CAA/D;AACA,UAAMC,oBAAoB,GAAG1B,cAAH,aAAGA,cAAH,cAAGA,cAAH,GAAqBoB,UAAU,CAACK,KAAX,GAAmB,CAAlE;AAEA,UAAME,WAAW,GACfZ,QAAQ,CAACU,KAAT,KAAmB,CAAnB,GACIN,SAAS,CAACM,KADd,GAEIV,QAAQ,CAACU,KAAT,KAAmB,CAAC,CAApB,GACE,CAACL,UAAU,CAACK,KADd,GAEE,CALR;AAOA,UAAMG,UAAU,GAAGZ,QAAQ,CAACS,KAAT,GAAiBpC,QAAjB,GAA4BsC,WAA/C;AAEAV,IAAAA,kBAAkB,CAACQ,KAAnB,GAA2BrD,WAAW,CACpCwD,UADoC,EAEpC,CACE,CAACR,UAAU,CAACK,KAAZ,GAAoB,CADtB,EAEE,CAACL,UAAU,CAACK,KAFd,EAGEN,SAAS,CAACM,KAHZ,EAIEN,SAAS,CAACM,KAAV,GAAkB,CAJpB,CAFoC,EAQpC,CACE,CAACL,UAAU,CAACK,KAAZ,IACGC,oBAAoB,GAAG,IAAIpC,iBAAP,GAA2B,CADlD,CADF,EAGE,CAAC8B,UAAU,CAACK,KAHd,EAIEN,SAAS,CAACM,KAJZ,EAKEN,SAAS,CAACM,KAAV,IAAmBD,mBAAmB,GAAG,IAAIlC,iBAAP,GAA2B,CAAjE,CALF,CARoC,CAAtC;AAiBA+B,IAAAA,gBAAgB,CAACI,KAAjB,GACEN,SAAS,CAACM,KAAV,GAAkB,CAAlB,GACIrD,WAAW,CACT6C,kBAAkB,CAACQ,KADV,EAET,CAAC,CAAC,CAAF,EAAK,CAAL,EAAQN,SAAS,CAACM,KAAlB,CAFS,EAGT,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAHS,CADf,GAMI,CAPN;AASAH,IAAAA,iBAAiB,CAACG,KAAlB,GACEL,UAAU,CAACK,KAAX,GAAmB,CAAnB,GACIrD,WAAW,CACT6C,kBAAkB,CAACQ,KADV,EAET,CAAC,CAACL,UAAU,CAACK,KAAb,EAAoB,CAApB,EAAuB,CAAvB,CAFS,EAGT,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAHS,CADf,GAMI,CAPN;AAQD,GAjDsC,EAiDpC,CACDR,kBADC,EAED5B,QAFC,EAGD8B,SAHC,EAID7B,iBAJC,EAKD8B,UALC,EAMDL,QANC,EAODM,gBAPC,EAQDC,iBARC,EASDN,QATC,EAUDjB,aAVC,EAWDC,cAXC,CAjDoC,CAAvC;AA+DA,QAAM6B,uBAAuB,GAAGjE,WAAW,CACzC,CAACkE,SAAD,EAAoBC,OAApB,KAAwC;AACtC;;AACA,QAAIA,OAAO,GAAG,CAAV,IAAexB,mBAAnB,EAAwC;AACtCjC,MAAAA,OAAO,CAACiC,mBAAD,CAAP,CAA6BvB,cAAc,CAACgD,KAA5C;AACD,KAFD,MAEO,IAAID,OAAO,GAAG,CAAV,IAAexB,mBAAnB,EAAwC;AAC7CjC,MAAAA,OAAO,CAACiC,mBAAD,CAAP,CAA6BvB,cAAc,CAACiD,IAA5C;AACD,KAFM,MAEA,IAAIzB,oBAAJ,EAA0B;AAC/BlC,MAAAA,OAAO,CAACkC,oBAAD,CAAP,CACEsB,SAAS,GAAG,CAAZ,GAAgB9C,cAAc,CAACiD,IAA/B,GAAsCjD,cAAc,CAACgD,KADvD;AAGD;AACF,GAZwC,EAazC,CAACxB,oBAAD,EAAuBD,mBAAvB,CAbyC,CAA3C;AAgBA,QAAM2B,iBAAiB,GAAGtE,WAAW,CACnC,CAACkE,SAAD,EAAoBC,OAApB,KAAwC;AACtC;;AACA,QAAIA,OAAO,GAAG,CAAV,IAAetB,eAAnB,EAAoC;AAClCnC,MAAAA,OAAO,CAACmC,eAAD,CAAP,CAAyBzB,cAAc,CAACgD,KAAxC;AACD,KAFD,MAEO,IAAID,OAAO,GAAG,CAAV,IAAetB,eAAnB,EAAoC;AACzCnC,MAAAA,OAAO,CAACmC,eAAD,CAAP,CAAyBzB,cAAc,CAACiD,IAAxC;AACD,KAFM,MAEA,IAAIvB,gBAAJ,EAAsB;AAC3BpC,MAAAA,OAAO,CAACoC,gBAAD,CAAP,CACEoB,SAAS,GAAG,CAAZ,GAAgB9C,cAAc,CAACiD,IAA/B,GAAsCjD,cAAc,CAACgD,KADvD;AAGD;AACF,GAZkC,EAanC,CAACtB,gBAAD,EAAmBD,eAAnB,CAbmC,CAArC;AAgBA,QAAM0B,UAAyD,GAC7DvE,WAAW,CACT,CAACmE,OAAD,EAAkBK,SAAlB,KAAyC;AACvC;;AAEA,UAAMC,uBAAuB,GAAG;AAC9BC,MAAAA,IAAI,EAAE,CADwB;AAE9BC,MAAAA,OAAO,EAAE,IAFqB;AAG9BC,MAAAA,SAAS,EAAE,GAHmB;AAI9BC,MAAAA,QAAQ,EAAEL,SAJoB;AAK9BM,MAAAA,iBAAiB,EAAE,IALW;AAM9BC,MAAAA,YAAY,EAAExE,YAAY,CAACyE,MANG;AAO9B,SAAG9C;AAP2B,KAAhC;AAUA,UAAM+C,SAAS,GAAGd,OAAO,KAAK,CAA9B;AACA,UAAMe,WAAW,GAAGD,SAAS,GAAG9B,QAAQ,CAACU,KAAT,GAAiB,CAApB,GAAwBM,OAAO,GAAG,CAA/D;AAEA,UAAMgB,SAAS,GAAGF,SAAS,GACvBC,WAAW,GACT1B,UAAU,CAACK,KADF,GAETN,SAAS,CAACM,KAHW,GAIvBqB,WAAW,GACT3B,SAAS,CAACM,KADD,GAETL,UAAU,CAACK,KANjB;AAQA,UAAMuB,oBAAoB,GAAG,EAC3B,GAAGX,uBADwB;AAE3BY,MAAAA,yBAAyB,EAAE,IAFA;AAG3BC,MAAAA,kBAAkB,EAAE,IAHO;AAI3BT,MAAAA,QAAQ,EACNL,SAAS,IACThE,WAAW,CAACgE,SAAD,EAAY,CAAC,CAACW,SAAF,EAAaA,SAAb,CAAZ,EAAqC,CAAC,CAAC,CAAF,EAAK,CAAL,CAArC;AANc,KAA7B;AASA,UAAMI,cAAc,GAAGpC,QAAQ,CAACU,KAAhC;AAEAR,IAAAA,kBAAkB,CAACQ,KAAnB,GAA2B9C,UAAU,CACnCoD,OADmC,EAEnCM,uBAFmC,EAGlCe,UAAD,IAAgB;AACd,UAAIA,UAAJ,EAAgB;AACdlB,QAAAA,iBAAiB,CAACiB,cAAD,EAAiBpB,OAAjB,CAAjB;AACD;AACF,KAPkC,CAArC;AAUA,UAAMsB,cAAc,GAAGtB,OAAO,KAAK,CAAZ,GAAgB,CAAhB,GAAoB,IAAIuB,IAAI,CAACC,IAAL,CAAUxB,OAAV,CAA/C;AAEAV,IAAAA,gBAAgB,CAACI,KAAjB,GAAyB9C,UAAU,CACjC2E,IAAI,CAACE,GAAL,CAASH,cAAT,EAAyB,CAAzB,CADiC,EAEjCL,oBAFiC,CAAnC;AAKA1B,IAAAA,iBAAiB,CAACG,KAAlB,GAA0B9C,UAAU,CAClC2E,IAAI,CAACE,GAAL,CAAS,CAACH,cAAV,EAA0B,CAA1B,CADkC,EAElCL,oBAFkC,CAApC;AAKAnB,IAAAA,uBAAuB,CAACsB,cAAD,EAAiBpB,OAAjB,CAAvB;AAEAhB,IAAAA,QAAQ,CAACU,KAAT,GAAiB6B,IAAI,CAACC,IAAL,CAAUxB,OAAV,CAAjB;AACD,GA7DQ,EA8DT,CACEhB,QADF,EAEEjB,gBAFF,EAGEmB,kBAHF,EAIEI,gBAJF,EAKEF,SALF,EAMEG,iBANF,EAOEF,UAPF,EAQES,uBARF,EASEK,iBATF,CA9DS,CADb;AA4EA,QAAMuB,aAAa,GAAGjF,cAAc,EAApC;AACA,QAAMkF,oBAAoB,GAAGlF,cAAc,EAA3C;AACA,QAAMmF,cAAc,GAAGnF,cAAc,EAArC;AAEA,QAAMoF,mBAAmB,GAAGhG,WAAW,CAAC,MAAM;AAC5C;;AAD4C;;AAE5C,UAAMiG,UAAU,GAAGxF,OAAO,CAACoF,aAAD,CAA1B;AACA,UAAMK,iBAAiB,GAAGzF,OAAO,CAACqF,oBAAD,CAAjC;AACA,UAAMK,WAAW,GAAG1F,OAAO,CAACsF,cAAD,CAA3B;AACAxC,IAAAA,SAAS,CAACM,KAAV,GACE,sBAACoC,UAAD,aAACA,UAAD,uBAACA,UAAU,CAAEG,KAAb,iEAAsB,CAAtB,8BAA4BF,iBAA5B,aAA4BA,iBAA5B,uBAA4BA,iBAAiB,CAAEE,KAA/C,yEAAwD,CAAxD,CADF;AAGA5C,IAAAA,UAAU,CAACK,KAAX,GACEP,QAAQ,CAACO,KAAT,0BACCsC,WADD,aACCA,WADD,uBACCA,WAAW,CAAEC,KADd,mEACuB9C,QAAQ,CAACO,KADhC,+BAECqC,iBAFD,aAECA,iBAFD,uBAECA,iBAAiB,CAAEE,KAFpB,2EAE6B,CAF7B,CADF;AAID,GAZsC,EAYpC,CACDP,aADC,EAEDC,oBAFC,EAGDC,cAHC,EAIDxC,SAJC,EAKDC,UALC,EAMDF,QANC,CAZoC,CAAvC;AAqBA,QAAM+C,gBAAgB,GAAGnG,OAAO,CAC9B,OAAO;AACLoG,IAAAA,KAAK,GAAG;AACN;;AACA,UAAIC,QAAJ,EAAc;AACZhC,QAAAA,UAAU,CAAC,CAAD,CAAV;AACA;AACD;;AACD5D,MAAAA,OAAO,CAAC,MAAM;AACZ4D,QAAAA,UAAU,CAAC,CAAD,CAAV;AACD,OAFM,CAAP;AAGD,KAVI;;AAWLiC,IAAAA,QAAQ,GAAG;AACT;;AACA,UAAID,QAAJ,EAAc;AACZP,QAAAA,mBAAmB;AACnBzB,QAAAA,UAAU,CAAChB,SAAS,CAACM,KAAX,CAAV;AACA;AACD;;AACDlD,MAAAA,OAAO,CAAC,MAAM;AACZqF,QAAAA,mBAAmB;AACnBzB,QAAAA,UAAU,CAAChB,SAAS,CAACM,KAAX,CAAV;AACD,OAHM,CAAP;AAID,KAtBI;;AAuBL4C,IAAAA,SAAS,GAAG;AACV;;AACA,UAAIF,QAAJ,EAAc;AACZP,QAAAA,mBAAmB;AACnBzB,QAAAA,UAAU,CAAC,CAACf,UAAU,CAACK,KAAb,CAAV;AACA;AACD;;AACDlD,MAAAA,OAAO,CAAC,MAAM;AACZqF,QAAAA,mBAAmB;AACnBzB,QAAAA,UAAU,CAAC,CAACf,UAAU,CAACK,KAAb,CAAV;AACD,OAHM,CAAP;AAID,KAlCI;;AAmCL6C,IAAAA,KAAK,GAAG;AACN;;AACAtD,MAAAA,QAAQ,CAACS,KAAT,GAAiB,CAAjB;AACAJ,MAAAA,gBAAgB,CAACI,KAAjB,GAAyB,CAAzB;AACAR,MAAAA,kBAAkB,CAACQ,KAAnB,GAA2B,CAA3B;AACAV,MAAAA,QAAQ,CAACU,KAAT,GAAiB,CAAjB;AACD;;AAzCI,GAAP,CAD8B,EA4C9B,CACEU,UADF,EAEEyB,mBAFF,EAGEzC,SAHF,EAIEC,UAJF,EAKEJ,QALF,EAMEK,gBANF,EAOEJ,kBAPF,EAQEF,QARF,CA5C8B,CAAhC;AAwDA,QAAMwD,WAAW,GAAG3G,WAAW,CAC7B,CAAC;AAAE4G,IAAAA;AAAF,GAAD,KAAwC;AACtCtD,IAAAA,QAAQ,CAACO,KAAT,GAAiB+C,WAAW,CAACC,MAAZ,CAAmBC,KAApC;AACD,GAH4B,EAI7B,CAACxD,QAAD,CAJ6B,CAA/B,CA7SA,CAoTA;AACA;;AAEA,QAAMyD,mBAAmB,GAAGlG,gBAAgB,CAAC,MAAM;AACjD,WAAO;AACLmG,MAAAA,OAAO,EAAEvD,gBAAgB,CAACI,KAAjB,KAA2B,CAA3B,GAA+B,CAA/B,GAAmC;AADvC,KAAP;AAGD,GAJ2C,CAA5C;AAMA,QAAMoD,WAAW,GAAGjH,WAAW,CAC7B,mBACE,oBAAC,QAAD,CAAU,IAAV;AACE,IAAA,GAAG,EAAE8F,oBADP;AAEE,IAAA,KAAK,EAAE,CAACoB,MAAM,CAACC,WAAR,EAAqBJ,mBAArB;AAFT,KAGGhE,iBAHH,aAGGA,iBAHH,uBAGGA,iBAAiB,CAChBU,gBADgB,EAEhBJ,kBAFgB,EAGhBgD,gBAHgB,CAHpB,eAQE,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,GAAG,EAAER;AAApB,IARF,CAF2B,EAa7B,CACExC,kBADF,EAEE0D,mBAFF,EAGElB,aAHF,EAIEC,oBAJF,EAKE/C,iBALF,EAMEU,gBANF,EAOE4C,gBAPF,CAb6B,CAA/B;AAwBA,QAAMe,oBAAoB,GAAGvG,gBAAgB,CAAC,MAAM;AAClD,WAAO;AACLmG,MAAAA,OAAO,EAAEtD,iBAAiB,CAACG,KAAlB,KAA4B,CAA5B,GAAgC,CAAhC,GAAoC;AADxC,KAAP;AAGD,GAJ4C,CAA7C;AAMA,QAAMwD,YAAY,GAAGrH,WAAW,CAC9B,mBACE,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACkH,MAAM,CAACI,YAAR,EAAsBF,oBAAtB;AAAtB,KACGpE,kBADH,aACGA,kBADH,uBACGA,kBAAkB,CACjBU,iBADiB,EAEjBL,kBAFiB,EAGjBgD,gBAHiB,CADrB,eAME,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,GAAG,EAAEN;AAApB,IANF,CAF4B,EAW9B,CACE1C,kBADF,EAEEL,kBAFF,EAGEoE,oBAHF,EAIErB,cAJF,EAKErC,iBALF,EAME2C,gBANF,CAX8B,CAAhC;AAqBA,QAAMkB,aAAa,GAAGvH,WAAW,CAC9BwH,KAAD,IAAmE;AACjE;;AACA,UAAM;AAAEhD,MAAAA;AAAF,QAAgBgD,KAAtB;AACApE,IAAAA,QAAQ,CAACS,KAAT,GAAiB2D,KAAK,CAACC,YAAvB;AAEA,UAAMC,iBAAiB,GAAG7F,aAAH,aAAGA,aAAH,cAAGA,aAAH,GAAoB0B,SAAS,CAACM,KAAV,GAAkB,CAA7D;AACA,UAAM8D,kBAAkB,GAAG7F,cAAH,aAAGA,cAAH,cAAGA,cAAH,GAAqB0B,UAAU,CAACK,KAAX,GAAmB,CAAhE;AAEA,UAAM4D,YAAY,GAChB,CAACrE,QAAQ,CAACS,KAAT,GAAiB1C,SAAS,GAAGqD,SAA9B,IAA2C/C,QAD7C;AAGA,QAAI0C,OAAO,GAAG,CAAd;;AAEA,QAAIhB,QAAQ,CAACU,KAAT,KAAmB,CAAvB,EAA0B;AACxB,UAAI4D,YAAY,GAAGC,iBAAnB,EAAsC;AACpCvD,QAAAA,OAAO,GAAGZ,SAAS,CAACM,KAApB;AACD,OAFD,MAEO,IAAI4D,YAAY,GAAG,CAACE,kBAApB,EAAwC;AAC7CxD,QAAAA,OAAO,GAAG,CAACX,UAAU,CAACK,KAAtB;AACD;AACF,KAND,MAMO,IAAIV,QAAQ,CAACU,KAAT,KAAmB,CAAvB,EAA0B;AAC/B;AACA,UAAI4D,YAAY,GAAG,CAACC,iBAApB,EAAuC;AACrCvD,QAAAA,OAAO,GAAGZ,SAAS,CAACM,KAApB;AACD;AACF,KALM,MAKA;AACL;AACA,UAAI4D,YAAY,GAAGE,kBAAnB,EAAuC;AACrCxD,QAAAA,OAAO,GAAG,CAACX,UAAU,CAACK,KAAtB;AACD;AACF;;AAEDU,IAAAA,UAAU,CAACJ,OAAD,EAAUK,SAAS,GAAG/C,QAAtB,CAAV;AACD,GAjC8B,EAkC/B,CACE8C,UADF,EAEE9C,QAFF,EAGEI,aAHF,EAIE0B,SAJF,EAKEzB,cALF,EAME0B,UANF,EAOEL,QAPF,EAQEC,QARF,CAlC+B,CAAjC;AA8CA,QAAMkD,KAAK,GAAGtG,WAAW,CAAC,MAAM;AAC9B;;AACAuE,IAAAA,UAAU,CAAC,CAAD,CAAV;AACD,GAHwB,EAGtB,CAACA,UAAD,CAHsB,CAAzB;AAKA,QAAMqD,WAAW,GAAG9G,cAAc,CAAU,KAAV,CAAlC;AAEA,QAAM+G,UAAU,GAAG3H,OAAO,CAAC,MAAM;AAC/B,UAAM4H,GAAG,GAAG1H,OAAO,CAAC2H,GAAR,GACTC,uBADS,CACe,IADf,EAETC,OAFS,CAED,MAAM;AACb,UAAI9E,QAAQ,CAACU,KAAT,KAAmB,CAAvB,EAA0B;AACxByC,QAAAA,KAAK;AACN;AACF,KANS,CAAZ;;AAQA,QAAI,CAACrD,+BAAL,EAAsC;AACpC,aAAO6E,GAAP;AACD;;AAED,QAAII,KAAK,CAACC,OAAN,CAAclF,+BAAd,CAAJ,EAAoD;AAClD6E,MAAAA,GAAG,CAAC7E,+BAAJ,CAAoC,GAAGA,+BAAvC;AACD,KAFD,MAEO;AACL6E,MAAAA,GAAG,CAAC7E,+BAAJ,CAAoCA,+BAApC;AACD;;AAED,WAAO6E,GAAP;AACD,GApByB,EAoBvB,CAACxB,KAAD,EAAQnD,QAAR,EAAkBF,+BAAlB,CApBuB,CAA1B;AAsBA,QAAMmF,UAAU,GAAGlI,OAAO,CAAC,MAAM;AAC/B,UAAMmI,GAAG,GAAGjI,OAAO,CAACkI,GAAR,GACTvG,OADS,CACDA,OAAO,KAAK,KADX,EAETH,8BAFS,CAEsBA,8BAFtB,EAGT2G,aAHS,CAGK,CAAC,CAAC/F,uBAAF,EAA2BD,sBAA3B,CAHL,EAIT0F,OAJS,CAIDjC,mBAJC,EAKTwC,QALS,CAMPhB,KAAD,IAA8D;AAC5DpE,MAAAA,QAAQ,CAACS,KAAT,GAAiB2D,KAAK,CAACC,YAAvB;AAEA,YAAMgB,SAAS,GACbtF,QAAQ,CAACU,KAAT,KAAmB,CAAC,CAApB,GACIzC,cAAc,CAACgD,KADnB,GAEIjB,QAAQ,CAACU,KAAT,KAAmB,CAAnB,GACEzC,cAAc,CAACiD,IADjB,GAEEmD,KAAK,CAACC,YAAN,GAAqB,CAArB,GACErG,cAAc,CAACgD,KADjB,GAEEhD,cAAc,CAACiD,IAPzB;;AASA,UAAI,CAACuD,WAAW,CAAC/D,KAAjB,EAAwB;AACtB+D,QAAAA,WAAW,CAAC/D,KAAZ,GAAoB,IAApB;;AACA,YAAIV,QAAQ,CAACU,KAAT,KAAmB,CAAnB,IAAwBpB,wBAA5B,EAAsD;AACpD/B,UAAAA,OAAO,CAAC+B,wBAAD,CAAP,CAAkCgG,SAAlC;AACD,SAFD,MAEO,IAAI/F,yBAAJ,EAA+B;AACpChC,UAAAA,OAAO,CAACgC,yBAAD,CAAP,CAAmC+F,SAAnC;AACD;AACF;;AAED9E,MAAAA,mBAAmB;AACpB,KA5BO,EA8BT+E,KA9BS,CA+BPlB,KAAD,IAAmE;AACjED,MAAAA,aAAa,CAACC,KAAD,CAAb;AACD,KAjCO,EAmCTmB,UAnCS,CAmCE,MAAM;AAChBf,MAAAA,WAAW,CAAC/D,KAAZ,GAAoB,KAApB;AACD,KArCS,CAAZ;;AAuCA,QAAI,CAACZ,+BAAL,EAAsC;AACpC,aAAOoF,GAAP;AACD;;AAED,QAAIH,KAAK,CAACC,OAAN,CAAclF,+BAAd,CAAJ,EAAoD;AAClDoF,MAAAA,GAAG,CAACpF,+BAAJ,CAAoC,GAAGA,+BAAvC;AACD,KAFD,MAEO;AACLoF,MAAAA,GAAG,CAACpF,+BAAJ,CAAoCA,+BAApC;AACD;;AAED,WAAOoF,GAAP;AACD,GAnDyB,EAmDvB,CACD9F,sBADC,EAEDC,uBAFC,EAGDoF,WAHC,EAIDhG,8BAJC,EAKDG,OALC,EAMDwF,aANC,EAOD7E,yBAPC,EAQDD,wBARC,EASDU,QATC,EAUDQ,mBAVC,EAWDqC,mBAXC,EAYD5C,QAZC,EAaDH,+BAbC,CAnDuB,CAA1B;AAmEAhD,EAAAA,mBAAmB,CAACsB,GAAD,EAAM,MAAM8E,gBAAZ,EAA8B,CAACA,gBAAD,CAA9B,CAAnB;AAEA,QAAMuC,aAAa,GAAG/H,gBAAgB,CACpC,OAAO;AACLgI,IAAAA,SAAS,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAEzF,kBAAkB,CAACQ;AAAjC,KAAD,CADN;AAELkF,IAAAA,aAAa,EAAE5F,QAAQ,CAACU,KAAT,KAAmB,CAAnB,GAAuB,MAAvB,GAAgC;AAF1C,GAAP,CADoC,EAKpC,CAACR,kBAAD,EAAqBF,QAArB,CALoC,CAAtC;AAQA,QAAM6F,kBAAkB,gBACtB,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAEZ,UAA1B;AAAsC,IAAA,WAAW,EAAC;AAAlD,kBACE,oBAAC,QAAD,CAAU,IAAV,eACMlF,cADN;AAEE,IAAA,QAAQ,EAAEyD,WAFZ;AAGE,IAAA,KAAK,EAAE,CAACO,MAAM,CAAC+B,SAAR,EAAmBjH,cAAnB;AAHT,MAIGiF,WAAW,EAJd,EAKGI,YAAY,EALf,eAME,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAEQ,UAA1B;AAAsC,IAAA,WAAW,EAAC;AAAlD,kBACE,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACe,aAAD,EAAgB3G,sBAAhB;AAAtB,KACGK,QADH,CADF,CANF,CADF,CADF;AAiBA,SAAOD,MAAM,gBACX,oBAAC,IAAD;AAAM,IAAA,MAAM,EAAEA;AAAd,KAAuB2G,kBAAvB,CADW,GAGXA,kBAHF;AAKD,CAliByB,CAA5B;AAqiBA,eAAe3H,SAAf;AAGA,MAAM6F,MAAM,GAAGjG,UAAU,CAACiI,MAAX,CAAkB;AAC/BD,EAAAA,SAAS,EAAE;AACTE,IAAAA,QAAQ,EAAE;AADD,GADoB;AAI/BhC,EAAAA,WAAW,EAAE,EACX,GAAGlG,UAAU,CAACmI,kBADH;AAEXC,IAAAA,aAAa,EAAErI,WAAW,CAACsI,KAAZ,GAAoB,aAApB,GAAoC,KAFxC;AAGXH,IAAAA,QAAQ,EAAE;AAHC,GAJkB;AAS/B7B,EAAAA,YAAY,EAAE,EACZ,GAAGrG,UAAU,CAACmI,kBADF;AAEZC,IAAAA,aAAa,EAAErI,WAAW,CAACsI,KAAZ,GAAoB,KAApB,GAA4B,aAF/B;AAGZH,IAAAA,QAAQ,EAAE;AAHE;AATiB,CAAlB,CAAf", "sourcesContent": ["// Similarily to the DrawerLayout component this deserves to be put in a\n// separate repo. Although, keeping it here for the time being will allow us to\n// move faster and fix possible issues quicker\n\nimport React, {\n  ForwardedRef,\n  forwardRef,\n  useCallback,\n  useImperativeHandle,\n  useMemo,\n} from 'react';\nimport { GestureRef } from '../handlers/gestures/gesture';\nimport { GestureObjects as Gesture } from '../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../handlers/gestures/GestureDetector';\nimport {\n  GestureStateChangeEvent,\n  GestureUpdateEvent,\n} from '../handlers/gestureHandlerCommon';\nimport type { PanGestureHandlerProps } from '../handlers/PanGestureHandler';\nimport type { PanGestureHandlerEventPayload } from '../handlers/GestureHandlerEventPayload';\nimport Animated, {\n  ReduceMotion,\n  SharedValue,\n  interpolate,\n  measure,\n  runOnJS,\n  runOnUI,\n  useAnimatedRef,\n  useAnimatedStyle,\n  useSharedValue,\n  withSpring,\n} from 'react-native-reanimated';\nimport {\n  I18nManager,\n  LayoutChangeEvent,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nconst DRAG_TOSS = 0.05;\n\ntype SwipeableExcludes = Exclude<\n  keyof PanGestureHandlerProps,\n  'onGestureEvent' | 'onHandlerStateChange'\n>;\n\nenum SwipeDirection {\n  LEFT = 'left',\n  RIGHT = 'right',\n}\n\nexport interface SwipeableProps\n  extends Pick<PanGestureHandlerProps, SwipeableExcludes> {\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * `enableTrackpadTwoFingerGesture` swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance. e.g. value of 1 will indicate that the swipeable panel\n   * should exactly follow the gesture, 2 means it is going to be two times\n   * \"slower\".\n   */\n  friction?: number;\n\n  /**\n   * Distance from the left edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  leftThreshold?: number;\n\n  /**\n   * Distance from the right edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  rightThreshold?: number;\n\n  /**\n   * Distance that the panel must be dragged from the left edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromLeftEdge?: number;\n\n  /**\n   * Distance that the panel must be dragged from the right edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromRightEdge?: number;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the left\n   * actions panel's width. It is set to true by default as long as the left\n   * panel render method is present.\n   */\n  overshootLeft?: boolean;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the\n   * right actions panel's width. It is set to true by default as long as the\n   * right panel render method is present.\n   */\n  overshootRight?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance at overshoot. Default value is 1, it mean no friction, for\n   * a native feel, try 8 or above.\n   */\n  overshootFriction?: number;\n\n  /**\n   * Called when action panel gets open (either right or left).\n   */\n  onSwipeableOpen?: (\n    direction: SwipeDirection.LEFT | SwipeDirection.RIGHT\n  ) => void;\n\n  /**\n   * Called when action panel is closed.\n   */\n  onSwipeableClose?: (\n    direction: SwipeDirection.LEFT | SwipeDirection.RIGHT\n  ) => void;\n\n  /**\n   * Called when action panel starts animating on open (either right or left).\n   */\n  onSwipeableWillOpen?: (\n    direction: SwipeDirection.LEFT | SwipeDirection.RIGHT\n  ) => void;\n\n  /**\n   * Called when action panel starts animating on close.\n   */\n  onSwipeableWillClose?: (\n    direction: SwipeDirection.LEFT | SwipeDirection.RIGHT\n  ) => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to open.\n   */\n  onSwipeableOpenStartDrag?: (\n    direction: SwipeDirection.LEFT | SwipeDirection.RIGHT\n  ) => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to close.\n   */\n  onSwipeableCloseStartDrag?: (\n    direction: SwipeDirection.LEFT | SwipeDirection.RIGHT\n  ) => void;\n\n  /**\n   * `progress`: Equals `0` when `swipeable` is closed, `1` when `swipeable` is opened.\n   *  - When the element overshoots it's opened position the value tends towards `Infinity`.\n   *  - Goes back to `1` when `swipeable` is released.\n   *\n   * `translation`: a horizontal offset of the `swipeable` relative to its closed position.\\\n   * `swipeableMethods`: provides an object exposing methods for controlling the `swipeable`.\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderLeftActions?: (\n    progress: SharedValue<number>,\n    translation: SharedValue<number>,\n    swipeableMethods: SwipeableMethods\n  ) => React.ReactNode;\n\n  /**\n   * `progress`: Equals `0` when `swipeable` is closed, `1` when `swipeable` is opened.\n   *  - When the element overshoots it's opened position the value tends towards `Infinity`.\n   *  - Goes back to `1` when `swipeable` is released.\n   *\n   * `translation`: a horizontal offset of the `swipeable` relative to its closed position.\\\n   * `swipeableMethods`: provides an object exposing methods for controlling the `swipeable`.\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderRightActions?: (\n    progress: SharedValue<number>,\n    translation: SharedValue<number>,\n    swipeableMethods: SwipeableMethods\n  ) => React.ReactNode;\n\n  animationOptions?: Record<string, unknown>;\n\n  /**\n   * Style object for the container (`Animated.View`), for example to override\n   * `overflow: 'hidden'`.\n   */\n  containerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Style object for the children container (`Animated.View`), for example to\n   * apply `flex: 1`\n   */\n  childrenContainerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * A gesture object or an array of gesture objects containing the configuration and callbacks to be\n   * used with the swipeable's gesture handler.\n   */\n  simultaneousWithExternalGesture?:\n    | Exclude<GestureRef, number>\n    | Exclude<GestureRef, number>[];\n}\n\nexport interface SwipeableMethods {\n  close: () => void;\n  openLeft: () => void;\n  openRight: () => void;\n  reset: () => void;\n}\n\nconst Swipeable = forwardRef<SwipeableMethods, SwipeableProps>(\n  function Swipeable(\n    props: SwipeableProps,\n    ref: ForwardedRef<SwipeableMethods>\n  ) {\n    const defaultProps = {\n      friction: 1,\n      overshootFriction: 1,\n      dragOffset: 10,\n      enableTrackpadTwoFingerGesture: false,\n    };\n\n    const {\n      leftThreshold,\n      rightThreshold,\n      enabled,\n      containerStyle,\n      childrenContainerStyle,\n      animationOptions,\n      overshootLeft,\n      overshootRight,\n      testID,\n      children,\n      enableTrackpadTwoFingerGesture = defaultProps.enableTrackpadTwoFingerGesture,\n      dragOffsetFromLeftEdge = defaultProps.dragOffset,\n      dragOffsetFromRightEdge = defaultProps.dragOffset,\n      friction = defaultProps.friction,\n      overshootFriction = defaultProps.overshootFriction,\n      onSwipeableOpenStartDrag,\n      onSwipeableCloseStartDrag,\n      onSwipeableWillOpen,\n      onSwipeableWillClose,\n      onSwipeableOpen,\n      onSwipeableClose,\n      renderLeftActions,\n      renderRightActions,\n      simultaneousWithExternalGesture,\n      ...remainingProps\n    } = props;\n\n    const rowState = useSharedValue<number>(0);\n\n    const userDrag = useSharedValue<number>(0);\n\n    const appliedTranslation = useSharedValue<number>(0);\n\n    const rowWidth = useSharedValue<number>(0);\n    const leftWidth = useSharedValue<number>(0);\n    const rightWidth = useSharedValue<number>(0);\n\n    const showLeftProgress = useSharedValue<number>(0);\n    const showRightProgress = useSharedValue<number>(0);\n\n    const updateAnimatedEvent = useCallback(() => {\n      'worklet';\n\n      const shouldOvershootLeft = overshootLeft ?? leftWidth.value > 0;\n      const shouldOvershootRight = overshootRight ?? rightWidth.value > 0;\n\n      const startOffset =\n        rowState.value === 1\n          ? leftWidth.value\n          : rowState.value === -1\n            ? -rightWidth.value\n            : 0;\n\n      const offsetDrag = userDrag.value / friction + startOffset;\n\n      appliedTranslation.value = interpolate(\n        offsetDrag,\n        [\n          -rightWidth.value - 1,\n          -rightWidth.value,\n          leftWidth.value,\n          leftWidth.value + 1,\n        ],\n        [\n          -rightWidth.value -\n            (shouldOvershootRight ? 1 / overshootFriction : 0),\n          -rightWidth.value,\n          leftWidth.value,\n          leftWidth.value + (shouldOvershootLeft ? 1 / overshootFriction : 0),\n        ]\n      );\n\n      showLeftProgress.value =\n        leftWidth.value > 0\n          ? interpolate(\n              appliedTranslation.value,\n              [-1, 0, leftWidth.value],\n              [0, 0, 1]\n            )\n          : 0;\n\n      showRightProgress.value =\n        rightWidth.value > 0\n          ? interpolate(\n              appliedTranslation.value,\n              [-rightWidth.value, 0, 1],\n              [1, 0, 0]\n            )\n          : 0;\n    }, [\n      appliedTranslation,\n      friction,\n      leftWidth,\n      overshootFriction,\n      rightWidth,\n      rowState,\n      showLeftProgress,\n      showRightProgress,\n      userDrag,\n      overshootLeft,\n      overshootRight,\n    ]);\n\n    const dispatchImmediateEvents = useCallback(\n      (fromValue: number, toValue: number) => {\n        'worklet';\n        if (toValue > 0 && onSwipeableWillOpen) {\n          runOnJS(onSwipeableWillOpen)(SwipeDirection.RIGHT);\n        } else if (toValue < 0 && onSwipeableWillOpen) {\n          runOnJS(onSwipeableWillOpen)(SwipeDirection.LEFT);\n        } else if (onSwipeableWillClose) {\n          runOnJS(onSwipeableWillClose)(\n            fromValue > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT\n          );\n        }\n      },\n      [onSwipeableWillClose, onSwipeableWillOpen]\n    );\n\n    const dispatchEndEvents = useCallback(\n      (fromValue: number, toValue: number) => {\n        'worklet';\n        if (toValue > 0 && onSwipeableOpen) {\n          runOnJS(onSwipeableOpen)(SwipeDirection.RIGHT);\n        } else if (toValue < 0 && onSwipeableOpen) {\n          runOnJS(onSwipeableOpen)(SwipeDirection.LEFT);\n        } else if (onSwipeableClose) {\n          runOnJS(onSwipeableClose)(\n            fromValue > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT\n          );\n        }\n      },\n      [onSwipeableClose, onSwipeableOpen]\n    );\n\n    const animateRow: (toValue: number, velocityX?: number) => void =\n      useCallback(\n        (toValue: number, velocityX?: number) => {\n          'worklet';\n\n          const translationSpringConfig = {\n            mass: 2,\n            damping: 1000,\n            stiffness: 700,\n            velocity: velocityX,\n            overshootClamping: true,\n            reduceMotion: ReduceMotion.System,\n            ...animationOptions,\n          };\n\n          const isClosing = toValue === 0;\n          const moveToRight = isClosing ? rowState.value < 0 : toValue > 0;\n\n          const usedWidth = isClosing\n            ? moveToRight\n              ? rightWidth.value\n              : leftWidth.value\n            : moveToRight\n              ? leftWidth.value\n              : rightWidth.value;\n\n          const progressSpringConfig = {\n            ...translationSpringConfig,\n            restDisplacementThreshold: 0.01,\n            restSpeedThreshold: 0.01,\n            velocity:\n              velocityX &&\n              interpolate(velocityX, [-usedWidth, usedWidth], [-1, 1]),\n          };\n\n          const frozenRowState = rowState.value;\n\n          appliedTranslation.value = withSpring(\n            toValue,\n            translationSpringConfig,\n            (isFinished) => {\n              if (isFinished) {\n                dispatchEndEvents(frozenRowState, toValue);\n              }\n            }\n          );\n\n          const progressTarget = toValue === 0 ? 0 : 1 * Math.sign(toValue);\n\n          showLeftProgress.value = withSpring(\n            Math.max(progressTarget, 0),\n            progressSpringConfig\n          );\n\n          showRightProgress.value = withSpring(\n            Math.max(-progressTarget, 0),\n            progressSpringConfig\n          );\n\n          dispatchImmediateEvents(frozenRowState, toValue);\n\n          rowState.value = Math.sign(toValue);\n        },\n        [\n          rowState,\n          animationOptions,\n          appliedTranslation,\n          showLeftProgress,\n          leftWidth,\n          showRightProgress,\n          rightWidth,\n          dispatchImmediateEvents,\n          dispatchEndEvents,\n        ]\n      );\n\n    const leftLayoutRef = useAnimatedRef();\n    const leftWrapperLayoutRef = useAnimatedRef();\n    const rightLayoutRef = useAnimatedRef();\n\n    const updateElementWidths = useCallback(() => {\n      'worklet';\n      const leftLayout = measure(leftLayoutRef);\n      const leftWrapperLayout = measure(leftWrapperLayoutRef);\n      const rightLayout = measure(rightLayoutRef);\n      leftWidth.value =\n        (leftLayout?.pageX ?? 0) - (leftWrapperLayout?.pageX ?? 0);\n\n      rightWidth.value =\n        rowWidth.value -\n        (rightLayout?.pageX ?? rowWidth.value) +\n        (leftWrapperLayout?.pageX ?? 0);\n    }, [\n      leftLayoutRef,\n      leftWrapperLayoutRef,\n      rightLayoutRef,\n      leftWidth,\n      rightWidth,\n      rowWidth,\n    ]);\n\n    const swipeableMethods = useMemo<SwipeableMethods>(\n      () => ({\n        close() {\n          'worklet';\n          if (_WORKLET) {\n            animateRow(0);\n            return;\n          }\n          runOnUI(() => {\n            animateRow(0);\n          })();\n        },\n        openLeft() {\n          'worklet';\n          if (_WORKLET) {\n            updateElementWidths();\n            animateRow(leftWidth.value);\n            return;\n          }\n          runOnUI(() => {\n            updateElementWidths();\n            animateRow(leftWidth.value);\n          })();\n        },\n        openRight() {\n          'worklet';\n          if (_WORKLET) {\n            updateElementWidths();\n            animateRow(-rightWidth.value);\n            return;\n          }\n          runOnUI(() => {\n            updateElementWidths();\n            animateRow(-rightWidth.value);\n          })();\n        },\n        reset() {\n          'worklet';\n          userDrag.value = 0;\n          showLeftProgress.value = 0;\n          appliedTranslation.value = 0;\n          rowState.value = 0;\n        },\n      }),\n      [\n        animateRow,\n        updateElementWidths,\n        leftWidth,\n        rightWidth,\n        userDrag,\n        showLeftProgress,\n        appliedTranslation,\n        rowState,\n      ]\n    );\n\n    const onRowLayout = useCallback(\n      ({ nativeEvent }: LayoutChangeEvent) => {\n        rowWidth.value = nativeEvent.layout.width;\n      },\n      [rowWidth]\n    );\n\n    // As stated in `Dimensions.get` docstring, this function should be called on every render\n    // since dimensions may change (e.g. orientation change)\n\n    const leftActionAnimation = useAnimatedStyle(() => {\n      return {\n        opacity: showLeftProgress.value === 0 ? 0 : 1,\n      };\n    });\n\n    const leftElement = useCallback(\n      () => (\n        <Animated.View\n          ref={leftWrapperLayoutRef}\n          style={[styles.leftActions, leftActionAnimation]}>\n          {renderLeftActions?.(\n            showLeftProgress,\n            appliedTranslation,\n            swipeableMethods\n          )}\n          <Animated.View ref={leftLayoutRef} />\n        </Animated.View>\n      ),\n      [\n        appliedTranslation,\n        leftActionAnimation,\n        leftLayoutRef,\n        leftWrapperLayoutRef,\n        renderLeftActions,\n        showLeftProgress,\n        swipeableMethods,\n      ]\n    );\n\n    const rightActionAnimation = useAnimatedStyle(() => {\n      return {\n        opacity: showRightProgress.value === 0 ? 0 : 1,\n      };\n    });\n\n    const rightElement = useCallback(\n      () => (\n        <Animated.View style={[styles.rightActions, rightActionAnimation]}>\n          {renderRightActions?.(\n            showRightProgress,\n            appliedTranslation,\n            swipeableMethods\n          )}\n          <Animated.View ref={rightLayoutRef} />\n        </Animated.View>\n      ),\n      [\n        appliedTranslation,\n        renderRightActions,\n        rightActionAnimation,\n        rightLayoutRef,\n        showRightProgress,\n        swipeableMethods,\n      ]\n    );\n\n    const handleRelease = useCallback(\n      (event: GestureStateChangeEvent<PanGestureHandlerEventPayload>) => {\n        'worklet';\n        const { velocityX } = event;\n        userDrag.value = event.translationX;\n\n        const leftThresholdProp = leftThreshold ?? leftWidth.value / 2;\n        const rightThresholdProp = rightThreshold ?? rightWidth.value / 2;\n\n        const translationX =\n          (userDrag.value + DRAG_TOSS * velocityX) / friction;\n\n        let toValue = 0;\n\n        if (rowState.value === 0) {\n          if (translationX > leftThresholdProp) {\n            toValue = leftWidth.value;\n          } else if (translationX < -rightThresholdProp) {\n            toValue = -rightWidth.value;\n          }\n        } else if (rowState.value === 1) {\n          // Swiped to left\n          if (translationX > -leftThresholdProp) {\n            toValue = leftWidth.value;\n          }\n        } else {\n          // Swiped to right\n          if (translationX < rightThresholdProp) {\n            toValue = -rightWidth.value;\n          }\n        }\n\n        animateRow(toValue, velocityX / friction);\n      },\n      [\n        animateRow,\n        friction,\n        leftThreshold,\n        leftWidth,\n        rightThreshold,\n        rightWidth,\n        rowState,\n        userDrag,\n      ]\n    );\n\n    const close = useCallback(() => {\n      'worklet';\n      animateRow(0);\n    }, [animateRow]);\n\n    const dragStarted = useSharedValue<boolean>(false);\n\n    const tapGesture = useMemo(() => {\n      const tap = Gesture.Tap()\n        .shouldCancelWhenOutside(true)\n        .onStart(() => {\n          if (rowState.value !== 0) {\n            close();\n          }\n        });\n\n      if (!simultaneousWithExternalGesture) {\n        return tap;\n      }\n\n      if (Array.isArray(simultaneousWithExternalGesture)) {\n        tap.simultaneousWithExternalGesture(...simultaneousWithExternalGesture);\n      } else {\n        tap.simultaneousWithExternalGesture(simultaneousWithExternalGesture);\n      }\n\n      return tap;\n    }, [close, rowState, simultaneousWithExternalGesture]);\n\n    const panGesture = useMemo(() => {\n      const pan = Gesture.Pan()\n        .enabled(enabled !== false)\n        .enableTrackpadTwoFingerGesture(enableTrackpadTwoFingerGesture)\n        .activeOffsetX([-dragOffsetFromRightEdge, dragOffsetFromLeftEdge])\n        .onStart(updateElementWidths)\n        .onUpdate(\n          (event: GestureUpdateEvent<PanGestureHandlerEventPayload>) => {\n            userDrag.value = event.translationX;\n\n            const direction =\n              rowState.value === -1\n                ? SwipeDirection.RIGHT\n                : rowState.value === 1\n                  ? SwipeDirection.LEFT\n                  : event.translationX > 0\n                    ? SwipeDirection.RIGHT\n                    : SwipeDirection.LEFT;\n\n            if (!dragStarted.value) {\n              dragStarted.value = true;\n              if (rowState.value === 0 && onSwipeableOpenStartDrag) {\n                runOnJS(onSwipeableOpenStartDrag)(direction);\n              } else if (onSwipeableCloseStartDrag) {\n                runOnJS(onSwipeableCloseStartDrag)(direction);\n              }\n            }\n\n            updateAnimatedEvent();\n          }\n        )\n        .onEnd(\n          (event: GestureStateChangeEvent<PanGestureHandlerEventPayload>) => {\n            handleRelease(event);\n          }\n        )\n        .onFinalize(() => {\n          dragStarted.value = false;\n        });\n\n      if (!simultaneousWithExternalGesture) {\n        return pan;\n      }\n\n      if (Array.isArray(simultaneousWithExternalGesture)) {\n        pan.simultaneousWithExternalGesture(...simultaneousWithExternalGesture);\n      } else {\n        pan.simultaneousWithExternalGesture(simultaneousWithExternalGesture);\n      }\n\n      return pan;\n    }, [\n      dragOffsetFromLeftEdge,\n      dragOffsetFromRightEdge,\n      dragStarted,\n      enableTrackpadTwoFingerGesture,\n      enabled,\n      handleRelease,\n      onSwipeableCloseStartDrag,\n      onSwipeableOpenStartDrag,\n      rowState,\n      updateAnimatedEvent,\n      updateElementWidths,\n      userDrag,\n      simultaneousWithExternalGesture,\n    ]);\n\n    useImperativeHandle(ref, () => swipeableMethods, [swipeableMethods]);\n\n    const animatedStyle = useAnimatedStyle(\n      () => ({\n        transform: [{ translateX: appliedTranslation.value }],\n        pointerEvents: rowState.value === 0 ? 'auto' : 'box-only',\n      }),\n      [appliedTranslation, rowState]\n    );\n\n    const swipeableComponent = (\n      <GestureDetector gesture={panGesture} touchAction=\"pan-y\">\n        <Animated.View\n          {...remainingProps}\n          onLayout={onRowLayout}\n          style={[styles.container, containerStyle]}>\n          {leftElement()}\n          {rightElement()}\n          <GestureDetector gesture={tapGesture} touchAction=\"pan-y\">\n            <Animated.View style={[animatedStyle, childrenContainerStyle]}>\n              {children}\n            </Animated.View>\n          </GestureDetector>\n        </Animated.View>\n      </GestureDetector>\n    );\n\n    return testID ? (\n      <View testID={testID}>{swipeableComponent}</View>\n    ) : (\n      swipeableComponent\n    );\n  }\n);\n\nexport default Swipeable;\nexport type SwipeableRef = ForwardedRef<SwipeableMethods>;\n\nconst styles = StyleSheet.create({\n  container: {\n    overflow: 'hidden',\n  },\n  leftActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',\n    overflow: 'hidden',\n  },\n  rightActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse',\n    overflow: 'hidden',\n  },\n});\n"]}