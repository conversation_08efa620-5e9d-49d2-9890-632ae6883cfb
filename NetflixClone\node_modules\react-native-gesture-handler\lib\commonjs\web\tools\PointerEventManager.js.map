{"version": 3, "sources": ["PointerEventManager.ts"], "names": ["POINTER_CAPTURE_EXCLUDE_LIST", "Set", "PointerEventManager", "EventManager", "constructor", "view", "Map", "event", "x", "clientX", "y", "clientY", "adaptedEvent", "mapEvent", "EventTypes", "DOWN", "target", "has", "tagName", "setPointerCapture", "pointerId", "markAsInBounds", "trackedPointers", "add", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "UP", "releasePointerCapture", "markAsOutOfBounds", "delete", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "MOVE", "hasPointerCapture", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "onPointerOutOfBounds", "lastPosition", "CANCEL", "onPointerCancel", "clear", "onPointerMoveOver", "onPointerMoveOut", "mouseButtonsMapper", "set", "MouseB<PERSON>on", "LEFT", "MIDDLE", "RIGHT", "BUTTON_4", "BUTTON_5", "Infinity", "registerListeners", "addEventListener", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointerCancelCallback", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lostPointerCaptureCallback", "unregisterListeners", "removeEventListener", "rect", "getBoundingClientRect", "scaleX", "scaleY", "offsetX", "left", "offsetY", "top", "pointerType", "PointerTypeMapping", "get", "PointerType", "OTHER", "button", "time", "timeStamp", "stylusData", "resetManager"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AAMA;;;;;;AAEA,MAAMA,4BAA4B,GAAG,IAAIC,GAAJ,CAAgB,CAAC,QAAD,EAAW,OAAX,CAAhB,CAArC;;AAEe,MAAMC,mBAAN,SAAkCC,qBAAlC,CAA4D;AAKzEC,EAAAA,WAAW,CAACC,IAAD,EAAoB;AAC7B,UAAMA,IAAN;;AAD6B,6CAJL,IAAIJ,GAAJ,EAIK;;AAAA,gDAHO,IAAIK,GAAJ,EAGP;;AAAA;;AAAA,iDAeAC,KAAD,IAAyB;AACrD,UAAI,CAAC,8BAAkB,KAAKF,IAAvB,EAA6B;AAAEG,QAAAA,CAAC,EAAED,KAAK,CAACE,OAAX;AAAoBC,QAAAA,CAAC,EAAEH,KAAK,CAACI;AAA7B,OAA7B,CAAL,EAA2E;AACzE;AACD;;AAED,YAAMC,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBO,uBAAWC,IAAhC,CAAnC;AACA,YAAMC,MAAM,GAAGT,KAAK,CAACS,MAArB;;AAEA,UAAI,CAAChB,4BAA4B,CAACiB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACG,iBAAP,CAAyBP,YAAY,CAACQ,SAAtC;AACD;;AAED,WAAKC,cAAL,CAAoBT,YAAY,CAACQ,SAAjC;AACA,WAAKE,eAAL,CAAqBC,GAArB,CAAyBX,YAAY,CAACQ,SAAtC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCZ,QAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAWY,uBAApC;AACA,aAAKC,YAAL,CAAkBf,YAAlB;AACD,OAHD,MAGO;AACL,aAAKgB,aAAL,CAAmBhB,YAAnB;AACD;AACF,KApC8B;;AAAA,+CAsCFL,KAAD,IAAyB;AACnD;AACA;AACA;AACA;AACA,UAAI,KAAKiB,qBAAL,KAA+B,CAAnC,EAAsC;AACpC;AACD;;AAED,YAAMZ,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBO,uBAAWe,EAAhC,CAAnC;AACA,YAAMb,MAAM,GAAGT,KAAK,CAACS,MAArB;;AAEA,UAAI,CAAChB,4BAA4B,CAACiB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACc,qBAAP,CAA6BlB,YAAY,CAACQ,SAA1C;AACD;;AAED,WAAKW,iBAAL,CAAuBnB,YAAY,CAACQ,SAApC;AACA,WAAKE,eAAL,CAAqBU,MAArB,CAA4BpB,YAAY,CAACQ,SAAzC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCZ,QAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAWmB,qBAApC;AACA,aAAKC,eAAL,CAAqBtB,YAArB;AACD,OAHD,MAGO;AACL,aAAKuB,WAAL,CAAiBvB,YAAjB;AACD;AACF,KA/D8B;;AAAA,iDAiEAL,KAAD,IAAyB;AACrD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBO,uBAAWsB,IAAhC,CAAnC;AACA,YAAMpB,MAAM,GAAGT,KAAK,CAACS,MAArB,CAFqD,CAIrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,UACE,CAACA,MAAM,CAACqB,iBAAP,CAAyB9B,KAAK,CAACa,SAA/B,CAAD,IACA,CAACpB,4BAA4B,CAACiB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAFH,EAGE;AACAF,QAAAA,MAAM,CAACG,iBAAP,CAAyBZ,KAAK,CAACa,SAA/B;AACD;;AAED,YAAMkB,QAAiB,GAAG,8BAAkB,KAAKjC,IAAvB,EAA6B;AACrDG,QAAAA,CAAC,EAAEI,YAAY,CAACJ,CADqC;AAErDE,QAAAA,CAAC,EAAEE,YAAY,CAACF;AAFqC,OAA7B,CAA1B;AAKA,YAAM6B,YAAoB,GAAG,KAAKC,gBAAL,CAAsBC,OAAtB,CAC3B7B,YAAY,CAACQ,SADc,CAA7B;;AAIA,UAAIkB,QAAJ,EAAc;AACZ,YAAIC,YAAY,GAAG,CAAnB,EAAsB;AACpB3B,UAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAW4B,KAApC;AACA,eAAKC,cAAL,CAAoB/B,YAApB;AACA,eAAKS,cAAL,CAAoBT,YAAY,CAACQ,SAAjC;AACD,SAJD,MAIO;AACL,eAAKwB,aAAL,CAAmBhC,YAAnB;AACD;AACF,OARD,MAQO;AACL,YAAI2B,YAAY,IAAI,CAApB,EAAuB;AACrB3B,UAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAW+B,KAApC;AACA,eAAKC,cAAL,CAAoBlC,YAApB;AACA,eAAKmB,iBAAL,CAAuBnB,YAAY,CAACQ,SAApC;AACD,SAJD,MAIO;AACL,eAAK2B,oBAAL,CAA0BnC,YAA1B;AACD;AACF;;AAED,WAAKoC,YAAL,CAAkBxC,CAAlB,GAAsBD,KAAK,CAACC,CAA5B;AACA,WAAKwC,YAAL,CAAkBtC,CAAlB,GAAsBH,KAAK,CAACG,CAA5B;AACD,KArH8B;;AAAA,mDAuHEH,KAAD,IAAyB;AACvD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBO,uBAAWmC,MAAhC,CAAnC;AAEA,WAAKC,eAAL,CAAqBtC,YAArB;AACA,WAAKmB,iBAAL,CAAuBnB,YAAY,CAACQ,SAApC;AACA,WAAKI,qBAAL,GAA6B,CAA7B;AACA,WAAKF,eAAL,CAAqB6B,KAArB;AACD,KA9H8B;;AAAA,kDAgIC5C,KAAD,IAAyB;AACtD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBO,uBAAW4B,KAAhC,CAAnC;AAEA,WAAKU,iBAAL,CAAuBxC,YAAvB;AACD,KApI8B;;AAAA,kDAsICL,KAAD,IAAyB;AACtD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBO,uBAAW+B,KAAhC,CAAnC;AAEA,WAAKQ,gBAAL,CAAsBzC,YAAtB;AACD,KA1I8B;;AAAA,wDA4IOL,KAAD,IAAyB;AAC5D,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBO,uBAAWmC,MAAhC,CAAnC;;AAEA,UAAI,KAAK3B,eAAL,CAAqBL,GAArB,CAAyBL,YAAY,CAACQ,SAAtC,CAAJ,EAAsD;AACpD;AACA;AACA,aAAK8B,eAAL,CAAqBtC,YAArB;AAEA,aAAKY,qBAAL,GAA6B,CAA7B;AACA,aAAKF,eAAL,CAAqB6B,KAArB;AACD;AACF,KAvJ8B;;AAG7B,SAAKG,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+BC,kCAAYC,IAA3C;AACA,SAAKH,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+BC,kCAAYE,MAA3C;AACA,SAAKJ,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+BC,kCAAYG,KAA3C;AACA,SAAKL,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+BC,kCAAYI,QAA3C;AACA,SAAKN,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+BC,kCAAYK,QAA3C;AAEA,SAAKb,YAAL,GAAoB;AAClBxC,MAAAA,CAAC,EAAE,CAACsD,QADc;AAElBpD,MAAAA,CAAC,EAAE,CAACoD;AAFc,KAApB;AAID;;AA4IMC,EAAAA,iBAAiB,GAAS;AAC/B,SAAK1D,IAAL,CAAU2D,gBAAV,CAA2B,aAA3B,EAA0C,KAAKC,mBAA/C;AACA,SAAK5D,IAAL,CAAU2D,gBAAV,CAA2B,WAA3B,EAAwC,KAAKE,iBAA7C;AACA,SAAK7D,IAAL,CAAU2D,gBAAV,CAA2B,aAA3B,EAA0C,KAAKG,mBAA/C;AACA,SAAK9D,IAAL,CAAU2D,gBAAV,CAA2B,eAA3B,EAA4C,KAAKI,qBAAjD,EAJ+B,CAM/B;AACA;AACA;AACA;;AACA,SAAK/D,IAAL,CAAU2D,gBAAV,CAA2B,cAA3B,EAA2C,KAAKK,oBAAhD;AACA,SAAKhE,IAAL,CAAU2D,gBAAV,CAA2B,cAA3B,EAA2C,KAAKM,oBAAhD;AACA,SAAKjE,IAAL,CAAU2D,gBAAV,CACE,oBADF,EAEE,KAAKO,0BAFP;AAID;;AAEMC,EAAAA,mBAAmB,GAAS;AACjC,SAAKnE,IAAL,CAAUoE,mBAAV,CAA8B,aAA9B,EAA6C,KAAKR,mBAAlD;AACA,SAAK5D,IAAL,CAAUoE,mBAAV,CAA8B,WAA9B,EAA2C,KAAKP,iBAAhD;AACA,SAAK7D,IAAL,CAAUoE,mBAAV,CAA8B,aAA9B,EAA6C,KAAKN,mBAAlD;AACA,SAAK9D,IAAL,CAAUoE,mBAAV,CAA8B,eAA9B,EAA+C,KAAKL,qBAApD;AACA,SAAK/D,IAAL,CAAUoE,mBAAV,CAA8B,cAA9B,EAA8C,KAAKJ,oBAAnD;AACA,SAAKhE,IAAL,CAAUoE,mBAAV,CAA8B,cAA9B,EAA8C,KAAKH,oBAAnD;AACA,SAAKjE,IAAL,CAAUoE,mBAAV,CACE,oBADF,EAEE,KAAKF,0BAFP;AAID;;AAES1D,EAAAA,QAAQ,CAACN,KAAD,EAAsBkB,SAAtB,EAA2D;AAAA;;AAC3E,UAAMiD,IAAI,GAAG,KAAKrE,IAAL,CAAUsE,qBAAV,EAAb;AACA,UAAM;AAAEC,MAAAA,MAAF;AAAUC,MAAAA;AAAV,QAAqB,+BAAmB,KAAKxE,IAAxB,CAA3B;AAEA,WAAO;AACLG,MAAAA,CAAC,EAAED,KAAK,CAACE,OADJ;AAELC,MAAAA,CAAC,EAAEH,KAAK,CAACI,OAFJ;AAGLmE,MAAAA,OAAO,EAAE,CAACvE,KAAK,CAACE,OAAN,GAAgBiE,IAAI,CAACK,IAAtB,IAA8BH,MAHlC;AAILI,MAAAA,OAAO,EAAE,CAACzE,KAAK,CAACI,OAAN,GAAgB+D,IAAI,CAACO,GAAtB,IAA6BJ,MAJjC;AAKLzD,MAAAA,SAAS,EAAEb,KAAK,CAACa,SALZ;AAMLK,MAAAA,SAAS,EAAEA,SANN;AAOLyD,MAAAA,WAAW,2BACTC,0BAAmBC,GAAnB,CAAuB7E,KAAK,CAAC2E,WAA7B,CADS,yEACoCG,yBAAYC,KARtD;AASLC,MAAAA,MAAM,EAAE,KAAKjC,kBAAL,CAAwB8B,GAAxB,CAA4B7E,KAAK,CAACgF,MAAlC,CATH;AAULC,MAAAA,IAAI,EAAEjF,KAAK,CAACkF,SAVP;AAWLC,MAAAA,UAAU,EAAE,iCAAqBnF,KAArB;AAXP,KAAP;AAaD;;AAEMoF,EAAAA,YAAY,GAAS;AAC1B,UAAMA,YAAN;AACA,SAAKrE,eAAL,CAAqB6B,KAArB;AACD;;AAnNwE", "sourcesContent": ["import EventManager from './EventManager';\nimport { MouseButton } from '../../handlers/gestureHandlerCommon';\nimport { AdaptedEvent, EventTypes, Point } from '../interfaces';\nimport {\n  PointerTypeMapping,\n  calculateViewScale,\n  tryExtractStylusData,\n  isPointerInBounds,\n} from '../utils';\nimport { PointerType } from '../../PointerType';\n\nconst POINTER_CAPTURE_EXCLUDE_LIST = new Set<string>(['SELECT', 'INPUT']);\n\nexport default class PointerEventManager extends EventManager<HTMLElement> {\n  private trackedPointers = new Set<number>();\n  private readonly mouseButtonsMapper = new Map<number, MouseButton>();\n  private lastPosition: Point;\n\n  constructor(view: HTMLElement) {\n    super(view);\n\n    this.mouseButtonsMapper.set(0, MouseButton.LEFT);\n    this.mouseButtonsMapper.set(1, MouseButton.MIDDLE);\n    this.mouseButtonsMapper.set(2, MouseButton.RIGHT);\n    this.mouseButtonsMapper.set(3, MouseButton.BUTTON_4);\n    this.mouseButtonsMapper.set(4, <PERSON>Button.BUTTON_5);\n\n    this.lastPosition = {\n      x: -Infinity,\n      y: -Infinity,\n    };\n  }\n\n  private pointerDownCallback = (event: PointerEvent) => {\n    if (!isPointerInBounds(this.view, { x: event.clientX, y: event.clientY })) {\n      return;\n    }\n\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.DOWN);\n    const target = event.target as HTMLElement;\n\n    if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n      target.setPointerCapture(adaptedEvent.pointerId);\n    }\n\n    this.markAsInBounds(adaptedEvent.pointerId);\n    this.trackedPointers.add(adaptedEvent.pointerId);\n\n    if (++this.activePointersCounter > 1) {\n      adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_DOWN;\n      this.onPointerAdd(adaptedEvent);\n    } else {\n      this.onPointerDown(adaptedEvent);\n    }\n  };\n\n  private pointerUpCallback = (event: PointerEvent) => {\n    // When we call reset on gesture handlers, it also resets their event managers\n    // In some handlers (like RotationGestureHandler) reset is called before all pointers leave view\n    // This means, that activePointersCounter will be set to 0, while there are still remaining pointers on view\n    // Removing them will end in activePointersCounter going below 0, therefore handlers won't behave properly\n    if (this.activePointersCounter === 0) {\n      return;\n    }\n\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.UP);\n    const target = event.target as HTMLElement;\n\n    if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n      target.releasePointerCapture(adaptedEvent.pointerId);\n    }\n\n    this.markAsOutOfBounds(adaptedEvent.pointerId);\n    this.trackedPointers.delete(adaptedEvent.pointerId);\n\n    if (--this.activePointersCounter > 0) {\n      adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_UP;\n      this.onPointerRemove(adaptedEvent);\n    } else {\n      this.onPointerUp(adaptedEvent);\n    }\n  };\n\n  private pointerMoveCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.MOVE);\n    const target = event.target as HTMLElement;\n\n    // You may be wondering why are we setting pointer capture here, when we\n    // already set it in `pointerdown` handler. Well, that's a great question,\n    // for which I don't have an answer. Specification (https://www.w3.org/TR/pointerevents2/#dom-element-setpointercapture)\n    // says that the requirement for `setPointerCapture` to work is that pointer\n    // must be in 'active buttons state`, otherwise it will fail silently, which\n    // is lovely. Obviously, when `pointerdown` is fired, one of the buttons\n    // (when using mouse) is pressed, but that doesn't mean that `setPointerCapture`\n    // will succeed, for some reason. Since it fails silently, we don't actually know\n    // if it worked or not (there's `gotpointercapture` event, but the complexity of\n    // incorporating it here seems stupid), so we just call it again here, every time\n    // pointer moves until it succeeds.\n    // God, I do love web development.\n    if (\n      !target.hasPointerCapture(event.pointerId) &&\n      !POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)\n    ) {\n      target.setPointerCapture(event.pointerId);\n    }\n\n    const inBounds: boolean = isPointerInBounds(this.view, {\n      x: adaptedEvent.x,\n      y: adaptedEvent.y,\n    });\n\n    const pointerIndex: number = this.pointersInBounds.indexOf(\n      adaptedEvent.pointerId\n    );\n\n    if (inBounds) {\n      if (pointerIndex < 0) {\n        adaptedEvent.eventType = EventTypes.ENTER;\n        this.onPointerEnter(adaptedEvent);\n        this.markAsInBounds(adaptedEvent.pointerId);\n      } else {\n        this.onPointerMove(adaptedEvent);\n      }\n    } else {\n      if (pointerIndex >= 0) {\n        adaptedEvent.eventType = EventTypes.LEAVE;\n        this.onPointerLeave(adaptedEvent);\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n      } else {\n        this.onPointerOutOfBounds(adaptedEvent);\n      }\n    }\n\n    this.lastPosition.x = event.x;\n    this.lastPosition.y = event.y;\n  };\n\n  private pointerCancelCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.CANCEL);\n\n    this.onPointerCancel(adaptedEvent);\n    this.markAsOutOfBounds(adaptedEvent.pointerId);\n    this.activePointersCounter = 0;\n    this.trackedPointers.clear();\n  };\n\n  private pointerEnterCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.ENTER);\n\n    this.onPointerMoveOver(adaptedEvent);\n  };\n\n  private pointerLeaveCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.LEAVE);\n\n    this.onPointerMoveOut(adaptedEvent);\n  };\n\n  private lostPointerCaptureCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.CANCEL);\n\n    if (this.trackedPointers.has(adaptedEvent.pointerId)) {\n      // In some cases the `pointerup` event is not fired, but `lostpointercapture` is.\n      // Here we simulate the `pointercancel` event to make sure the gesture handler stops tracking it.\n      this.onPointerCancel(adaptedEvent);\n\n      this.activePointersCounter = 0;\n      this.trackedPointers.clear();\n    }\n  };\n\n  public registerListeners(): void {\n    this.view.addEventListener('pointerdown', this.pointerDownCallback);\n    this.view.addEventListener('pointerup', this.pointerUpCallback);\n    this.view.addEventListener('pointermove', this.pointerMoveCallback);\n    this.view.addEventListener('pointercancel', this.pointerCancelCallback);\n\n    // onPointerEnter and onPointerLeave are triggered by a custom logic responsible for\n    // handling shouldCancelWhenOutside flag, and are unreliable unless the pointer is down.\n    // We therefore use pointerenter and pointerleave events to handle the hover gesture,\n    // mapping them to onPointerMoveOver and onPointerMoveOut respectively.\n    this.view.addEventListener('pointerenter', this.pointerEnterCallback);\n    this.view.addEventListener('pointerleave', this.pointerLeaveCallback);\n    this.view.addEventListener(\n      'lostpointercapture',\n      this.lostPointerCaptureCallback\n    );\n  }\n\n  public unregisterListeners(): void {\n    this.view.removeEventListener('pointerdown', this.pointerDownCallback);\n    this.view.removeEventListener('pointerup', this.pointerUpCallback);\n    this.view.removeEventListener('pointermove', this.pointerMoveCallback);\n    this.view.removeEventListener('pointercancel', this.pointerCancelCallback);\n    this.view.removeEventListener('pointerenter', this.pointerEnterCallback);\n    this.view.removeEventListener('pointerleave', this.pointerLeaveCallback);\n    this.view.removeEventListener(\n      'lostpointercapture',\n      this.lostPointerCaptureCallback\n    );\n  }\n\n  protected mapEvent(event: PointerEvent, eventType: EventTypes): AdaptedEvent {\n    const rect = this.view.getBoundingClientRect();\n    const { scaleX, scaleY } = calculateViewScale(this.view);\n\n    return {\n      x: event.clientX,\n      y: event.clientY,\n      offsetX: (event.clientX - rect.left) / scaleX,\n      offsetY: (event.clientY - rect.top) / scaleY,\n      pointerId: event.pointerId,\n      eventType: eventType,\n      pointerType:\n        PointerTypeMapping.get(event.pointerType) ?? PointerType.OTHER,\n      button: this.mouseButtonsMapper.get(event.button),\n      time: event.timeStamp,\n      stylusData: tryExtractStylusData(event),\n    };\n  }\n\n  public resetManager(): void {\n    super.resetManager();\n    this.trackedPointers.clear();\n  }\n}\n"]}