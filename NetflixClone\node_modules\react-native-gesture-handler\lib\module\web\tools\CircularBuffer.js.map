{"version": 3, "sources": ["CircularBuffer.ts"], "names": ["Circular<PERSON><PERSON>er", "constructor", "size", "capacity", "buffer", "Array", "index", "_size", "push", "element", "Math", "min", "get", "at", "clear"], "mappings": ";;AAAA,eAAe,MAAMA,cAAN,CAAwB;AAMrCC,EAAAA,WAAW,CAACC,IAAD,EAAe;AAAA;;AAAA;;AAAA;;AAAA;;AACxB,SAAKC,QAAL,GAAgBD,IAAhB;AACA,SAAKE,MAAL,GAAc,IAAIC,KAAJ,CAAaH,IAAb,CAAd;AACA,SAAKI,KAAL,GAAa,CAAb;AACA,SAAKC,KAAL,GAAa,CAAb;AACD;;AAEMC,EAAAA,IAAI,CAACC,OAAD,EAAmB;AAC5B,SAAKL,MAAL,CAAY,KAAKE,KAAjB,IAA0BG,OAA1B;AACA,SAAKH,KAAL,GAAa,CAAC,KAAKA,KAAL,GAAa,CAAd,IAAmB,KAAKH,QAArC;AACA,SAAKI,KAAL,GAAaG,IAAI,CAACC,GAAL,CAAS,KAAKT,IAAL,GAAY,CAArB,EAAwB,KAAKC,QAA7B,CAAb;AACD;;AAEMS,EAAAA,GAAG,CAACC,EAAD,EAAgB;AACxB,QAAI,KAAKN,KAAL,KAAe,KAAKJ,QAAxB,EAAkC;AAChC,UAAIG,KAAK,GAAG,CAAC,KAAKA,KAAL,GAAaO,EAAd,IAAoB,KAAKV,QAArC;;AACA,UAAIG,KAAK,GAAG,CAAZ,EAAe;AACbA,QAAAA,KAAK,IAAI,KAAKH,QAAd;AACD;;AAED,aAAO,KAAKC,MAAL,CAAYE,KAAZ,CAAP;AACD,KAPD,MAOO;AACL,aAAO,KAAKF,MAAL,CAAYS,EAAZ,CAAP;AACD;AACF;;AAEMC,EAAAA,KAAK,GAAS;AACnB,SAAKV,MAAL,GAAc,IAAIC,KAAJ,CAAa,KAAKF,QAAlB,CAAd;AACA,SAAKG,KAAL,GAAa,CAAb;AACA,SAAKC,KAAL,GAAa,CAAb;AACD;;AAEc,MAAJL,IAAI,GAAG;AAChB,WAAO,KAAKK,KAAZ;AACD;;AAxCoC", "sourcesContent": ["export default class CircularBuffer<T> {\n  private capacity: number;\n  private buffer: T[];\n  private index: number;\n  private _size: number;\n\n  constructor(size: number) {\n    this.capacity = size;\n    this.buffer = new Array<T>(size);\n    this.index = 0;\n    this._size = 0;\n  }\n\n  public push(element: T): void {\n    this.buffer[this.index] = element;\n    this.index = (this.index + 1) % this.capacity;\n    this._size = Math.min(this.size + 1, this.capacity);\n  }\n\n  public get(at: number): T {\n    if (this._size === this.capacity) {\n      let index = (this.index + at) % this.capacity;\n      if (index < 0) {\n        index += this.capacity;\n      }\n\n      return this.buffer[index];\n    } else {\n      return this.buffer[at];\n    }\n  }\n\n  public clear(): void {\n    this.buffer = new Array<T>(this.capacity);\n    this.index = 0;\n    this._size = 0;\n  }\n\n  public get size() {\n    return this._size;\n  }\n}\n"]}