{"version": 3, "sources": ["ManualGestureHandler.ts"], "names": ["Gesture<PERSON>andler", "ManualGestureHandler", "onPointerDown", "event", "tracker", "addToTracker", "begin", "tryToSendTouchEvent", "onPointerAdd", "onPointerMove", "track", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "onPointerRemove"], "mappings": "AACA,OAAOA,cAAP,MAA2B,kBAA3B;AAEA,eAAe,MAAMC,oBAAN,SAAmCD,cAAnC,CAAkD;AACrDE,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AACA,SAAKG,KAAL;AAEA,SAAKC,mBAAL,CAAyBJ,KAAzB;AACD;;AAESK,EAAAA,YAAY,CAACL,KAAD,EAA4B;AAChD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMK,YAAN,CAAmBL,KAAnB;AACD;;AAESM,EAAAA,aAAa,CAACN,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaM,KAAb,CAAmBP,KAAnB;AACA,UAAMM,aAAN,CAAoBN,KAApB;AACD;;AAESQ,EAAAA,oBAAoB,CAACR,KAAD,EAA4B;AACxD,SAAKC,OAAL,CAAaM,KAAb,CAAmBP,KAAnB;AACA,UAAMQ,oBAAN,CAA2BR,KAA3B;AACD;;AAESS,EAAAA,WAAW,CAACT,KAAD,EAA4B;AAC/C,UAAMS,WAAN,CAAkBT,KAAlB;AACA,SAAKC,OAAL,CAAaS,iBAAb,CAA+BV,KAAK,CAACW,SAArC;AACD;;AAESC,EAAAA,eAAe,CAACZ,KAAD,EAA4B;AACnD,UAAMY,eAAN,CAAsBZ,KAAtB;AACA,SAAKC,OAAL,CAAaS,iBAAb,CAA+BV,KAAK,CAACW,SAArC;AACD;;AAhC8D", "sourcesContent": ["import { AdaptedEvent } from '../interfaces';\nimport GestureHandler from './GestureHandler';\n\nexport default class ManualGestureHandler extends GestureHandler {\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n    this.begin();\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    super.onPointerOutOfBounds(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n}\n"]}