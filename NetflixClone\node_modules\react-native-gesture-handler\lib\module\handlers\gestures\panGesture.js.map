{"version": 3, "sources": ["panGesture.ts"], "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "changeX", "translationX", "changeY", "translationY", "PanGesture", "constructor", "handler<PERSON>ame", "activeOffsetY", "offset", "Array", "isArray", "config", "activeOffsetYStart", "activeOffsetYEnd", "activeOffsetX", "activeOffsetXStart", "activeOffsetXEnd", "failOffsetY", "failOffsetYStart", "failOffsetYEnd", "failOffsetX", "failOffsetXStart", "failOffsetXEnd", "minPointers", "maxPointers", "minDistance", "distance", "minDist", "minVelocity", "velocity", "minVelocityX", "minVelocityY", "averageTouches", "value", "avgTouches", "enableTrackpadTwoFingerGesture", "activateAfterLongPress", "duration", "onChange", "callback", "handlers"], "mappings": ";;AAAA,SAA4BA,oBAA5B,QAAwD,WAAxD;;AAUA,SAASC,qBAAT,CACEC,OADF,EAEEC,QAFF,EAGE;AACA;;AACA,MAAIC,aAAJ;;AACA,MAAID,QAAQ,KAAKE,SAAjB,EAA4B;AAC1BD,IAAAA,aAAa,GAAG;AACdE,MAAAA,OAAO,EAAEJ,OAAO,CAACK,YADH;AAEdC,MAAAA,OAAO,EAAEN,OAAO,CAACO;AAFH,KAAhB;AAID,GALD,MAKO;AACLL,IAAAA,aAAa,GAAG;AACdE,MAAAA,OAAO,EAAEJ,OAAO,CAACK,YAAR,GAAuBJ,QAAQ,CAACI,YAD3B;AAEdC,MAAAA,OAAO,EAAEN,OAAO,CAACO,YAAR,GAAuBN,QAAQ,CAACM;AAF3B,KAAhB;AAID;;AAED,SAAO,EAAE,GAAGP,OAAL;AAAc,OAAGE;AAAjB,GAAP;AACD;;AAED,OAAO,MAAMM,UAAN,SAAyBV,oBAAzB,CAGL;AAGAW,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAFwC,EAExC;;AAGZ,SAAKC,WAAL,GAAmB,mBAAnB;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,aAAa,CACXC,MADW,EAEX;AACA,QAAIC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAJ,EAA2B;AACzB,WAAKG,MAAL,CAAYC,kBAAZ,GAAiCJ,MAAM,CAAC,CAAD,CAAvC;AACA,WAAKG,MAAL,CAAYE,gBAAZ,GAA+BL,MAAM,CAAC,CAAD,CAArC;AACD,KAHD,MAGO,IAAIA,MAAM,GAAG,CAAb,EAAgB;AACrB,WAAKG,MAAL,CAAYC,kBAAZ,GAAiCJ,MAAjC;AACD,KAFM,MAEA;AACL,WAAKG,MAAL,CAAYE,gBAAZ,GAA+BL,MAA/B;AACD;;AACD,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEM,EAAAA,aAAa,CACXN,MADW,EAEX;AACA,QAAIC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAJ,EAA2B;AACzB,WAAKG,MAAL,CAAYI,kBAAZ,GAAiCP,MAAM,CAAC,CAAD,CAAvC;AACA,WAAKG,MAAL,CAAYK,gBAAZ,GAA+BR,MAAM,CAAC,CAAD,CAArC;AACD,KAHD,MAGO,IAAIA,MAAM,GAAG,CAAb,EAAgB;AACrB,WAAKG,MAAL,CAAYI,kBAAZ,GAAiCP,MAAjC;AACD,KAFM,MAEA;AACL,WAAKG,MAAL,CAAYK,gBAAZ,GAA+BR,MAA/B;AACD;;AACD,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACES,EAAAA,WAAW,CACTT,MADS,EAET;AACA,QAAIC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAJ,EAA2B;AACzB,WAAKG,MAAL,CAAYO,gBAAZ,GAA+BV,MAAM,CAAC,CAAD,CAArC;AACA,WAAKG,MAAL,CAAYQ,cAAZ,GAA6BX,MAAM,CAAC,CAAD,CAAnC;AACD,KAHD,MAGO,IAAIA,MAAM,GAAG,CAAb,EAAgB;AACrB,WAAKG,MAAL,CAAYO,gBAAZ,GAA+BV,MAA/B;AACD,KAFM,MAEA;AACL,WAAKG,MAAL,CAAYQ,cAAZ,GAA6BX,MAA7B;AACD;;AACD,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEY,EAAAA,WAAW,CACTZ,MADS,EAET;AACA,QAAIC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAJ,EAA2B;AACzB,WAAKG,MAAL,CAAYU,gBAAZ,GAA+Bb,MAAM,CAAC,CAAD,CAArC;AACA,WAAKG,MAAL,CAAYW,cAAZ,GAA6Bd,MAAM,CAAC,CAAD,CAAnC;AACD,KAHD,MAGO,IAAIA,MAAM,GAAG,CAAb,EAAgB;AACrB,WAAKG,MAAL,CAAYU,gBAAZ,GAA+Bb,MAA/B;AACD,KAFM,MAEA;AACL,WAAKG,MAAL,CAAYW,cAAZ,GAA6Bd,MAA7B;AACD;;AACD,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEe,EAAAA,WAAW,CAACA,WAAD,EAAsB;AAC/B,SAAKZ,MAAL,CAAYY,WAAZ,GAA0BA,WAA1B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,WAAW,CAACA,WAAD,EAAsB;AAC/B,SAAKb,MAAL,CAAYa,WAAZ,GAA0BA,WAA1B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKf,MAAL,CAAYgB,OAAZ,GAAsBD,QAAtB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEE,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKlB,MAAL,CAAYiB,WAAZ,GAA0BC,QAA1B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEC,EAAAA,YAAY,CAACD,QAAD,EAAmB;AAC7B,SAAKlB,MAAL,CAAYmB,YAAZ,GAA2BD,QAA3B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEE,EAAAA,YAAY,CAACF,QAAD,EAAmB;AAC7B,SAAKlB,MAAL,CAAYoB,YAAZ,GAA2BF,QAA3B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEG,EAAAA,cAAc,CAACC,KAAD,EAAiB;AAC7B,SAAKtB,MAAL,CAAYuB,UAAZ,GAAyBD,KAAzB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEE,EAAAA,8BAA8B,CAACF,KAAD,EAAiB;AAC7C,SAAKtB,MAAL,CAAYwB,8BAAZ,GAA6CF,KAA7C;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEG,EAAAA,sBAAsB,CAACC,QAAD,EAAmB;AACvC,SAAK1B,MAAL,CAAYyB,sBAAZ,GAAqCC,QAArC;AACA,WAAO,IAAP;AACD;;AAEDC,EAAAA,QAAQ,CACNC,QADM,EAMN;AACA;AACA,SAAKC,QAAL,CAAc7C,qBAAd,GAAsCA,qBAAtC;AACA,WAAO,MAAM2C,QAAN,CAAeC,QAAf,CAAP;AACD;;AAvLD", "sourcesContent": ["import { BaseGestureConfig, ContinousBaseGesture } from './gesture';\nimport { GestureUpdateEvent } from '../gestureHandlerCommon';\nimport { PanGestureConfig } from '../PanGestureHandler';\nimport type { PanGestureHandlerEventPayload } from '../GestureHandlerEventPayload';\n\nexport type PanGestureChangeEventPayload = {\n  changeX: number;\n  changeY: number;\n};\n\nfunction changeEventCalculator(\n  current: GestureUpdateEvent<PanGestureHandlerEventPayload>,\n  previous?: GestureUpdateEvent<PanGestureHandlerEventPayload>\n) {\n  'worklet';\n  let changePayload: PanGestureChangeEventPayload;\n  if (previous === undefined) {\n    changePayload = {\n      changeX: current.translationX,\n      changeY: current.translationY,\n    };\n  } else {\n    changePayload = {\n      changeX: current.translationX - previous.translationX,\n      changeY: current.translationY - previous.translationY,\n    };\n  }\n\n  return { ...current, ...changePayload };\n}\n\nexport class PanGesture extends ContinousBaseGesture<\n  PanGestureHandlerEventPayload,\n  PanGestureChangeEventPayload\n> {\n  public config: BaseGestureConfig & PanGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'PanGestureHandler';\n  }\n\n  /**\n   * Range along Y axis (in points) where fingers travels without activation of gesture.\n   * @param offset\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#activeoffsetyvalue-number--number\n   */\n  activeOffsetY(\n    offset: number | [activeOffsetYStart: number, activeOffsetYEnd: number]\n  ) {\n    if (Array.isArray(offset)) {\n      this.config.activeOffsetYStart = offset[0];\n      this.config.activeOffsetYEnd = offset[1];\n    } else if (offset < 0) {\n      this.config.activeOffsetYStart = offset;\n    } else {\n      this.config.activeOffsetYEnd = offset;\n    }\n    return this;\n  }\n\n  /**\n   * Range along X axis (in points) where fingers travels without activation of gesture.\n   * @param offset\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#activeoffsetxvalue-number--number\n   */\n  activeOffsetX(\n    offset: number | [activeOffsetXStart: number, activeOffsetXEnd: number]\n  ) {\n    if (Array.isArray(offset)) {\n      this.config.activeOffsetXStart = offset[0];\n      this.config.activeOffsetXEnd = offset[1];\n    } else if (offset < 0) {\n      this.config.activeOffsetXStart = offset;\n    } else {\n      this.config.activeOffsetXEnd = offset;\n    }\n    return this;\n  }\n\n  /**\n   * When the finger moves outside this range (in points) along Y axis and gesture hasn't yet activated it will fail recognizing the gesture.\n   * @param offset\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#failoffsetyvalue-number--number\n   */\n  failOffsetY(\n    offset: number | [failOffsetYStart: number, failOffsetYEnd: number]\n  ) {\n    if (Array.isArray(offset)) {\n      this.config.failOffsetYStart = offset[0];\n      this.config.failOffsetYEnd = offset[1];\n    } else if (offset < 0) {\n      this.config.failOffsetYStart = offset;\n    } else {\n      this.config.failOffsetYEnd = offset;\n    }\n    return this;\n  }\n\n  /**\n   * When the finger moves outside this range (in points) along X axis and gesture hasn't yet activated it will fail recognizing the gesture.\n   * @param offset\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#failoffsetxvalue-number--number\n   */\n  failOffsetX(\n    offset: number | [failOffsetXStart: number, failOffsetXEnd: number]\n  ) {\n    if (Array.isArray(offset)) {\n      this.config.failOffsetXStart = offset[0];\n      this.config.failOffsetXEnd = offset[1];\n    } else if (offset < 0) {\n      this.config.failOffsetXStart = offset;\n    } else {\n      this.config.failOffsetXEnd = offset;\n    }\n    return this;\n  }\n\n  /**\n   * A number of fingers that is required to be placed before gesture can activate. Should be a higher or equal to 0 integer.\n   * @param minPointers\n   */\n  minPointers(minPointers: number) {\n    this.config.minPointers = minPointers;\n    return this;\n  }\n\n  /**\n   * When the given number of fingers is placed on the screen and gesture hasn't yet activated it will fail recognizing the gesture.\n   * Should be a higher or equal to 0 integer.\n   * @param maxPointers\n   */\n  maxPointers(maxPointers: number) {\n    this.config.maxPointers = maxPointers;\n    return this;\n  }\n\n  /**\n   * Minimum distance the finger (or multiple finger) need to travel before the gesture activates.\n   * Expressed in points.\n   * @param distance\n   */\n  minDistance(distance: number) {\n    this.config.minDist = distance;\n    return this;\n  }\n\n  /**\n   * Minimum velocity the finger has to reach in order to activate handler.\n   * @param velocity\n   */\n  minVelocity(velocity: number) {\n    this.config.minVelocity = velocity;\n    return this;\n  }\n\n  /**\n   * Minimum velocity along X axis the finger has to reach in order to activate handler.\n   * @param velocity\n   */\n  minVelocityX(velocity: number) {\n    this.config.minVelocityX = velocity;\n    return this;\n  }\n\n  /**\n   * Minimum velocity along Y axis the finger has to reach in order to activate handler.\n   * @param velocity\n   */\n  minVelocityY(velocity: number) {\n    this.config.minVelocityY = velocity;\n    return this;\n  }\n\n  /**\n   * #### Android only\n   * Android, by default, will calculate translation values based on the position of the leading pointer (the first one that was placed on the screen).\n   * This modifier allows that behavior to be changed to the one that is default on iOS - the averaged position of all active pointers will be used to calculate the translation values.\n   * @param value\n   */\n  averageTouches(value: boolean) {\n    this.config.avgTouches = value;\n    return this;\n  }\n\n  /**\n   * #### iOS only\n   * Enables two-finger gestures on supported devices, for example iPads with trackpads.\n   * @param value\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture/#enabletrackpadtwofingergesturevalue-boolean-ios-only\n   */\n  enableTrackpadTwoFingerGesture(value: boolean) {\n    this.config.enableTrackpadTwoFingerGesture = value;\n    return this;\n  }\n\n  /**\n   * Duration in milliseconds of the LongPress gesture before Pan is allowed to activate.\n   * @param duration\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture/#activateafterlongpressduration-number\n   */\n  activateAfterLongPress(duration: number) {\n    this.config.activateAfterLongPress = duration;\n    return this;\n  }\n\n  onChange(\n    callback: (\n      event: GestureUpdateEvent<\n        PanGestureHandlerEventPayload & PanGestureChangeEventPayload\n      >\n    ) => void\n  ) {\n    // @ts-ignore TS being overprotective, PanGestureHandlerEventPayload is Record\n    this.handlers.changeEventCalculator = changeEventCalculator;\n    return super.onChange(callback);\n  }\n}\n\nexport type PanGestureType = InstanceType<typeof PanGesture>;\n"]}