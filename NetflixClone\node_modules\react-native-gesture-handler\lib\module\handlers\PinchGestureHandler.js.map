{"version": 3, "sources": ["PinchGestureHandler.ts"], "names": ["createHandler", "baseGestureHandlerProps", "pinchHandlerName", "PinchGestureHandler", "name", "allowedProps", "config"], "mappings": "AACA,OAAOA,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAKA;AACA;AACA;;AAIA,OAAO,MAAMC,gBAAgB,GAAG,qBAAzB;AAEP;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGH,aAAa,CAG9C;AACAI,EAAAA,IAAI,EAAEF,gBADN;AAEAG,EAAAA,YAAY,EAAEJ,uBAFd;AAGAK,EAAAA,MAAM,EAAE;AAHR,CAH8C,CAAzC", "sourcesContent": ["import { PinchGestureHandlerEventPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\n/**\n * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n */\nexport interface PinchGestureHandlerProps\n  extends BaseGestureHandlerProps<PinchGestureHandlerEventPayload> {}\n\nexport const pinchHandlerName = 'PinchGestureHandler';\n\n/**\n * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n */\nexport type PinchGestureHandler = typeof PinchGestureHandler;\n\n/**\n * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const PinchGestureHandler = createHandler<\n  PinchGestureHandlerProps,\n  PinchGestureHandlerEventPayload\n>({\n  name: pinchHandlerName,\n  allowedProps: baseGestureHandlerProps,\n  config: {},\n});\n"]}