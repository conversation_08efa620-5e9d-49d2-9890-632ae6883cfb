{"version": 3, "sources": ["dropHandlers.ts"], "names": ["unregister<PERSON><PERSON><PERSON>", "RNGestureHandlerModule", "scheduleFlushOperations", "MountRegistry", "dropHandlers", "preparedGesture", "handler", "attachedGestures", "dropGestureHandler", "handlerTag", "config", "testId", "gestureWillUnmount"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,wBAAlC;AACA,OAAOC,sBAAP,MAAmC,iCAAnC;AACA,SAASC,uBAAT,QAAwC,aAAxC;AAEA,SAASC,aAAT,QAA8B,wBAA9B;AAEA,OAAO,SAASC,YAAT,CAAsBC,eAAtB,EAA6D;AAClE,OAAK,MAAMC,OAAX,IAAsBD,eAAe,CAACE,gBAAtC,EAAwD;AACtDN,IAAAA,sBAAsB,CAACO,kBAAvB,CAA0CF,OAAO,CAACG,UAAlD;AAEAT,IAAAA,iBAAiB,CAACM,OAAO,CAACG,UAAT,EAAqBH,OAAO,CAACI,MAAR,CAAeC,MAApC,CAAjB;AAEAR,IAAAA,aAAa,CAACS,kBAAd,CAAiCN,OAAjC;AACD;;AAEDJ,EAAAA,uBAAuB;AACxB", "sourcesContent": ["import { unregisterHandler } from '../../handlersRegistry';\nimport RNGestureHandlerModule from '../../../RNGestureHandlerModule';\nimport { scheduleFlushOperations } from '../../utils';\nimport { AttachedGestureState } from './types';\nimport { MountRegistry } from '../../../mountRegistry';\n\nexport function dropHandlers(preparedGesture: AttachedGestureState) {\n  for (const handler of preparedGesture.attachedGestures) {\n    RNGestureHandlerModule.dropGestureHandler(handler.handlerTag);\n\n    unregisterHandler(handler.handlerTag, handler.config.testId);\n\n    MountRegistry.gestureWillUnmount(handler);\n  }\n\n  scheduleFlushOperations();\n}\n"]}